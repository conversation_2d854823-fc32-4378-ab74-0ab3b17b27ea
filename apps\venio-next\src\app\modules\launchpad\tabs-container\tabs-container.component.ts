import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  Injector,
  On<PERSON><PERSON>roy,
  OnInit,
  output,
  signal,
  TemplateRef,
  viewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import {
  SelectEvent,
  TabContentDirective,
  TabStripComponent,
  TabStripTabComponent,
} from '@progress/kendo-angular-layout'
import { chevronDownIcon } from '@progress/kendo-svg-icons'
import { LaunchpadSubToolbarComponent } from '../launchpad-sub-toolbar/launchpad-sub-toolbar.component'
import { LaunchpadToolbarComponent } from '../launchpad-toolbar/launchpad-toolbar.component'
import { CaseGridComponent } from '../case-grid/case-grid/case-grid.component'
import {
  ModuleLoginStateService,
  ProductionFacade,
  ProjectFacade,
  UserFacade,
} from '@venio/data-access/common'
import { PageArgs } from '@venio/ui/pagination'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  CaseDetailRequestInfo,
  LaunchpadAction,
  LaunchpadTabTypes,
  ResponseModel,
} from '@venio/shared/models/interfaces'
import { CommonActionTypes, ReportTypes } from '@venio/shared/models/constants'
import { toSignal } from '@angular/core/rxjs-interop'
import { cloneDeep } from 'lodash'
import { ReportsFacade } from '@venio/data-access/reports'
import {
  DialogService,
  WindowCloseResult,
  WindowModule,
  WindowRef,
  WindowService,
} from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SharedDocumentsContainerComponent } from '../shared-documents/shared-documents-container.component'
import {
  DocumentShareFacade,
  RightModel,
  SharedDocRequestType,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { ReviewsetContainerComponent } from '../reviewset-container/reviewset-container.component'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { debounceTime, filter, map, Subject, take, takeUntil } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { NotificationDialogComponent } from '@venio/feature/notification'

/**
 * Local state type for the toolbar change.
 */
type ToolbarChangeType =
  | 'CASE_SEARCH'
  | 'CASE_PAGING'
  | 'REVIEW_SET_PAGING'
  | 'REVIEW_SET_SEARCH'
  | 'DOCUMENT_SHARE_SEARCH'
  | 'DOCUMENT_SHARE_PAGING'

@Component({
  selector: 'venio-tabs-container',
  standalone: true,
  imports: [
    CommonModule,
    TabContentDirective,
    TabStripComponent,
    TabStripTabComponent,
    LaunchpadSubToolbarComponent,
    LaunchpadToolbarComponent,
    CaseGridComponent,
    ButtonsModule,
    WindowModule,
    SharedDocumentsContainerComponent,
    WindowModule,
    ReviewsetContainerComponent,
    SkeletonComponent,
  ],
  templateUrl: './tabs-container.component.html',
  styleUrl: './tabs-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabsContainerComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  public windowTitleBar = viewChild<TemplateRef<unknown>>('windowTitleBar')

  public tabstrip = viewChild<TabStripComponent>('tabstrip')

  public caseTab = viewChild<TabStripTabComponent>('caseTab')

  public reviewTab = viewChild<TabStripTabComponent>('reviewTab')

  public readonly launchpadTabTypes = LaunchpadTabTypes

  public readonly chevronDownIcon = chevronDownIcon

  private readonly injector = inject(Injector)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly dialogService = inject(DialogService)

  private readonly windowService = inject(WindowService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly projectFacade = inject(ProjectFacade)

  private readonly reportsFacade = inject(ReportsFacade)

  private readonly sharedDocFacade = inject(DocumentShareFacade)

  private readonly productionFacade = inject(ProductionFacade)

  private readonly userFacade = inject(UserFacade)

  private readonly moduleLoginState = inject(ModuleLoginStateService)

  private readonly permissionFacade = inject(StartupsFacade)

  private activatedRoute = inject(ActivatedRoute)

  private readonly notificationService = inject(NotificationService)

  private isReprocessWindowOpen = signal<boolean>(false)

  private readonly rightList = toSignal<RightModel>(
    this.permissionFacade.getUserRights$
  )

  private readonly shareDocList = toSignal(
    this.sharedDocFacade.selectSharedDocumentList$
  )

  /** Signal for the total case count */
  public readonly totalSharedDocs = computed<number>(() => {
    return this.shareDocList()?.length || 0
  })

  /** Emits the tab change event. */
  public readonly tabChanged = output<SelectEvent>()

  /**
   * Currently, we may have 3 tabs, 0 = cases, 1 = review set, 2 = document share.
   * Based on this index, we render the respective component.
   */
  public readonly selectedTabIndex = signal<LaunchpadTabTypes>(
    LaunchpadTabTypes.CASE
  )

  /**
   * Whether the favourite project is filtered.
   */
  private readonly isFavoriteProjectFilter = signal(false)

  /**
   * Grab the current user details from the user facade of observable and create a signal.
   */
  public readonly currentUser = toSignal(
    this.userFacade.selectCurrentUserDetails$
  )

  public readonly isCaseLoading = toSignal(
    this.projectFacade.selectIsCaseDetailLoading$
  )

  private readonly unIndexMediaStatus = toSignal(
    this.projectFacade.selectunIndexMediaSuccess$
  )

  public readonly isMediaStatusLoading = toSignal(
    this.projectFacade.selectIsMediaStatusLoading$,
    { initialValue: false }
  )

  /** Computed property for the media processed status */
  public readonly unIndexMediaList = computed(() => {
    return this.unIndexMediaStatus()?.data
  })

  /**
   * Whether the currently logged-in user global role is reviewer.
   * If the global role contains the word 'reviewer', then it is a reviewer which
   * only the reviewer can see the review set tab only.
   */
  public readonly isReviewer = computed(
    () =>
      this.currentUser()
        ?.globalRoleName?.trim()
        .match(/reviewer/i) !== null
  )

  /**
   * Whether there are any review set available so that we can show the review set tab.
   *
   * If no review set available, no permission to manage review set, and the user is not a reviewer,
   * then we should not show the review set tab.
   */
  public readonly anyReviewSet = toSignal(
    this.projectFacade.selectReviewSetSummaryDetail$.pipe(
      filter((s) => typeof s !== 'undefined'),
      map((s) => s?.totalReviewSetCount > 0),
      // once we reach this far, we can take only one value and unsubscribe.
      // Avoiding memory leaks and unnecessary track of the value changes.
      take(1)
    )
  )

  /**
   * Whether the currently logged-in user have permission to manage review set.
   */
  public readonly canManageReviewSet = computed(() => {
    const rights = this.rightList()

    if (!rights) {
      return false
    }

    const invalidGlobalRights = rights['Invalid_Global_Right_List']

    if (!invalidGlobalRights) return false

    return !invalidGlobalRights[UserRights.ALLOW_TO_MANAGE_REVIEW_SET]
  })

  public readonly canViewSharedDoc = computed(() => {
    const rights = this.rightList()

    if (!rights) {
      return false
    }

    const invalidGlobalRights = rights['Invalid_Global_Right_List']

    if (!invalidGlobalRights) return false

    return !invalidGlobalRights[UserRights.ALLOW_TO_VIEW_SHARED_DOCUMENT]
  })

  public windowTitle = signal<string>('')

  public imageIconUrl = signal<string>('')

  private readonly directExportUrl = 'assets/svg/icon-case-branch-tree.svg'

  private readonly reprocessingUrl = 'assets/svg/icon-reprocessing-mashup.svg'

  public projectToReprocess: number

  private readonly toDestroy$ = new Subject<void>()

  private projectIdForProductionStatus: number

  private isProductionStatusDialogOpened = signal<boolean>(false)

  constructor() {
    const popupType = this.activatedRoute.snapshot.queryParams['popup']
    if (popupType === 'reprocessing') {
      this.projectToReprocess =
        this.activatedRoute.snapshot.queryParams['projectId']
    }

    if (popupType === 'production-status') {
      this.projectIdForProductionStatus =
        this.activatedRoute.snapshot.queryParams['projectId']
    }
  }

  public ngOnInit(): void {
    this.#handleTabChangeEffect()
    this.#fetchReviewSetDetail()
    this.#fetchSharedDocsDetail()
    this.#handleSharedDocTabView()
  }

  public ngAfterViewInit(): void {
    this.#handleReprocessNotification()
    this.#handleProductionStatusNotification()
    this.#handleUploadInvitation()
    this.#hanldeParentNotifiedMessage()
  }

  /**
   * Handles the tab selection change.
   * @param {SelectEvent} e - The tab selection event.
   * @returns {void}
   */
  public onSelect(e: SelectEvent): void {
    const { title } = e
    this.selectedTabIndex.set(title as LaunchpadTabTypes)
    this.tabChanged.emit(e)
  }

  /**
   * Handles the paging control change.
   * @param {PageArgs} arg - Generic paging info.
   * @param {ToolbarChangeType} type - The type of paging. It can be a case of paging, review set paging, or document share paging.
   * @returns {void}
   */
  public pagingChanged(arg: PageArgs, type: ToolbarChangeType): void {
    // As soo as the paging changes, we need to update the paging info in the project facade.
    // we store pageSize, pageNumber only from this arg.
    this.#updatePagingInfo(arg, type)
    switch (type) {
      case 'CASE_PAGING':
        // Once the paging info is updated, we need to fetch the case detail.
        this.#fetchCaseDetail()
        break
      case 'REVIEW_SET_PAGING':
        break
      case 'DOCUMENT_SHARE_PAGING':
        break
    }
  }

  /**
   * Handles the search control change.
   * @param {string} value - The search term.
   * @param {ToolbarChangeType} type - The type of search. It can be a case search, review set search, or document share search.
   * @returns {void}
   */
  public searchControlChanged(value: string, type: ToolbarChangeType): void {
    // As soon as the search value changes, we need to update the search value in the project facade.
    this.#updateCaseDetailRequestInfo({ searchText: value })
    switch (type) {
      case 'CASE_SEARCH':
        // Once the search value is updated, we need to fetch the case detail.
        this.#fetchCaseDetail()
        break
      case 'REVIEW_SET_SEARCH':
        break
      case 'DOCUMENT_SHARE_SEARCH':
        break
    }
  }

  /**
   * Handles the case grid action click.
   * @param {LaunchpadAction} event - The launchpad action event.
   * @returns {void}
   */
  public caseGridActionClick(event: LaunchpadAction): void {
    const { actionType, content } = event
    const selectedCase = cloneDeep(content)
    const payload = {
      actionType,
      selectedCase,
      isCaseActionClick: true,
    }

    this.moduleLoginState.updateProjectId(selectedCase.projectId)

    switch (event.actionType) {
      case CommonActionTypes.FAVOURITE:
        {
          const isFavoriteProject = (selectedCase.isFavoriteProject =
            !selectedCase.isFavoriteProject)
          // Post the favourite action to the server.
          this.projectFacade.toggleFavouriteProject(
            selectedCase.projectId,
            isFavoriteProject
          )
        }
        break
      case CommonActionTypes.UPLOAD_NEW:
        {
          if (selectedCase?.isExportServiceCase) {
            this.imageIconUrl.set(this.directExportUrl)
            this.#openDirectExportDialog(selectedCase?.projectId, false)
          } else {
            this.#notifyParentApp(payload)
          }
        }
        break
      case CommonActionTypes.UPLOAD_REPROCESS:
        {
          this.windowTitle.set('Reprocess')
          this.imageIconUrl.set(this.reprocessingUrl)

          this.openReprocessWindow(selectedCase.projectId, false, null, null)
        }
        break
      case CommonActionTypes.PRODUCE_STATUS:
        this.#openProductionStatusDialog(selectedCase?.projectId)
        break
      case CommonActionTypes.UPLOAD_INVITE:
        this.#openUploadInviteDialog(selectedCase?.projectId)
        break
      default:
        // Notify the parent app about the action.
        // Currently, we are reusing the legacy pages so we need to notify the parent app about the action.
        // Once we migrate the pages to the new micro-app, we can directly call the service from the micro-app.
        // for example, favourite is new, and we use new code base without notifying the parent app.
        if (
          event.actionType === CommonActionTypes.REVIEW ||
          event.actionType === CommonActionTypes.ANALYZE
        )
          this.fetchUnIndexMediaStatus(selectedCase?.projectId)
        this.#notifyParentApp(payload)
    }
  }

  /**
   * Handles the case toolbar action click.
   * @param {CommonActionTypes} actionEvent - The common action type.
   * @returns {void}
   */
  public caseToolbarActionClick(
    actionEvent: Record<CommonActionTypes, object>
  ): void {
    const actionType = Object.keys(actionEvent)[0] as CommonActionTypes
    const menuRef = actionEvent[actionType]
    const payload = {
      actionType,
      isCaseActionClick: true,
    }

    switch (actionType) {
      case CommonActionTypes.FAVOURITE:
        this.#filterByFavoriteProject()
        break

      case CommonActionTypes.SEARCH:
        // We won't need search term, but we use selected option from dropdown to fetch case
        this.#updateCaseDetailRequestInfo({
          caseTypeFilter: menuRef['caseType'],
        })
        this.#fetchCaseDetail()
        break

      case CommonActionTypes.REPORT:
        this.#openReportsDialog(menuRef['type'] as ReportTypes)
        break

      case CommonActionTypes.DIRECT_EXPORT:
        this.#openDirectExportDialog()
        break

      default:
        // Notify the parent app about the action.
        // Currently, we are reusing the legacy pages so we need to notify the parent app about the action.
        // Once we migrate the pages to the new micro-app, we can directly call the service from the micro-app.
        // for example, favourite is new, and we use new code base without notifying the parent app.
        this.#notifyParentApp(payload)
    }
  }

  /**
   * Fetches the review set detail based on the review set request info stored in the project facade.
   * @remarks
   *  The review set request info is stored in state, and they are being updated before we fetch the review set detail.
   *  @see {@link ReviewsetContainerComponent}
   * @returns {void}
   */
  #fetchReviewSetDetail(): void {
    this.projectFacade.fetchReviewSetSummaryDetail()
  }

  #openUnIndexMediaStatusDialog(): void {
    import(
      '../media-processing-status-dialog/media-processing-status-dialog.component'
    ).then((td) => {
      const dialogRef = this.dialogService.open({
        content: td.MediaProcessingStatusDialogComponent,
        appendTo: this.viewContainerRef,
        maxWidth: '900px',
        maxHeight: '650px',
        width: '80%',
        height: '90vh',
      })

      dialogRef.result.subscribe((result: any) => {
        if (result.shouldRedirect) {
          this.#navigateToDocuments()
        }
      })
    })
  }

  #openProductionStatusDialog(
    projectId: number,
    isOpenFromEmail?: boolean
  ): void {
    this.productionFacade.fetchProductionStatus(projectId, -1, -1)

    import('../production-status/production-status.component').then(
      ({ ProductionStatusComponent }) => {
        const dialogRef = this.dialogService.open({
          content: ProductionStatusComponent,
          appendTo: this.viewContainerRef,
          height: '85vh',
          width: '70vw',
        })
        const productionShareDialog = dialogRef.content.instance
        productionShareDialog.selectedProjectIdData = projectId

        this.isProductionStatusDialogOpened.set(true)

        dialogRef.result
          .pipe(
            filter((res) => !!res),
            take(1)
          )
          .subscribe(() => {
            this.isProductionStatusDialogOpened.set(false)
            dialogRef.content.destroy()

            //send message to parent window to remove the query parameters so that
            //the production status dialog does not open again on refresh and notification link works properly
            this.iframeMessengerService.sendMessage({
              type: 'MICRO_APP_DATA_CHANGE',
              eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
              eventTriggeredFor: 'PARENT_WINDOW',
              iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
              payload: {
                type: MessageType.ROUTE_CHANGE,
                content: {
                  popup: 'production-status',
                  removeParameters: true,
                },
              } as MessageContent,
            })
          })
      }
    )
  }

  #openReportsDialog(type: ReportTypes): void {
    this.reportsFacade.storeReportType(type)

    import(
      '../../reports/tabular/report-container/report-container.component'
    ).then(({ ReportContainerComponent }) => {
      this.dialogService.open({
        content: ReportContainerComponent,
        title: 'Reports',
        appendTo: this.viewContainerRef,
        height: 'calc(100vh - 100px)',
        width: 'calc(100vw - 100px)',
      })
    })
  }

  #openUploadInviteDialog(projectId: number): void {
    import(
      '../invite-upload/invite-upload-container/invite-upload-container.component'
    ).then(({ InviteUploadContainerComponent }) => {
      const dialogRef = this.dialogService.open({
        content: InviteUploadContainerComponent,
        appendTo: this.viewContainerRef,
        height: '90vh',
        width: '70vw',
      })
      dialogRef.content.setInput('selectedProjectId', projectId)
      dialogRef.content.setInput('tabsViewContainerRef', this.viewContainerRef)
    })
  }

  #filterByFavoriteProject(): void {
    {
      const isFavoriteProjectFilter = !this.isFavoriteProjectFilter()
      this.isFavoriteProjectFilter.set(isFavoriteProjectFilter)
      const filterFavoriteProjects = this.isFavoriteProjectFilter()
      // Post the favourite action to the server.
      this.projectFacade.updateCaseDetailRequestInfo({
        filterFavoriteProjects,
      })

      this.#fetchCaseDetail()
    }
  }

  #handleUploadInvitation(): void {
    this.activatedRoute.queryParams
      .pipe(
        debounceTime(100),
        map((params) =>
          //lower case of all parameter keys
          Object.keys(params).reduce((acc, key) => {
            acc[key.toLowerCase()] = params[key]
            return acc
          }, {} as Record<string, any>)
        ),
        filter((params) => {
          const projectId = Number(params['projectid'])
          return (
            params['action']?.toLowerCase() === 'directexport' &&
            !isNaN(projectId) &&
            projectId > 0
          )
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((params) => {
        this.#checkUploadInvitationAccess(
          Number(params['projectid']),
          params['userwisetoken']
        )
      })
  }

  #checkUploadInvitationAccess(projectId: number, userWiseToken: string): void {
    this.projectFacade.checkUploadInvitationAccess(projectId, userWiseToken)
    this.projectFacade.selectUploadInvitationAccessResponse$
      .pipe(
        filter((a) => !!a),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        const resMessage = response.data.toUpperCase()
        if (resMessage === 'SUCCESS') {
          this.#openDirectExportDialog(projectId, false)
          return
        }

        const messageType: Type = {
          style: 'error',
        }
        if (resMessage === 'EXPIRED') {
          this.showNotification(
            'Upload link has expired. This link cannot be further used to upload data.',
            messageType
          )
        } else if (resMessage === 'PROJECT_DOES_NOT_EXIST') {
          this.showNotification('The Project does not exists.', messageType)
        } else if (resMessage === 'UNAUTHORIZED_ACCESS') {
          this.showNotification(
            `You don't have the right to access this page.`,
            messageType
          )
        } else {
          this.showNotification(
            'The upload invitation link is not valid.',
            messageType
          )
        }
      })
  }

  #openDirectExportDialog(projectId?: number, isCreationFlow = true): void {
    this.windowTitle.set('Direct Export')
    import('../direct-export/direct-export-container.component').then((td) => {
      const windowRef: WindowRef = this.windowService.open({
        content: td.DirectExportContainerComponent,
        left: 0,
        state: 'maximized',
        top: 100,
        cssClass: 'v-custom-window',
        titleBarContent: this.windowTitleBar(),
        resizable: false,
        draggable: false,
      })

      const directExportData = windowRef.content.instance
      directExportData.data = {
        existingCaseId: projectId,
        isCaseCreationFlow: isCreationFlow,
      }

      windowRef.result.subscribe((result) => {
        if (result instanceof WindowCloseResult) {
          this.#fetchCaseDetail()
        }
      })
    })
  }

  #updateCaseDetailRequestInfo(values?: Partial<CaseDetailRequestInfo>): void {
    this.projectFacade.updateCaseDetailRequestInfo({
      ...values,
    })
  }

  /**
   * Updates the paging info in the project facade.
   * @param {PageArgs} arg - Generic paging info.
   * @returns {void}
   */
  #updatePagingInfo(arg: PageArgs, type: ToolbarChangeType): void {
    const { pageNumber, pageSize } = arg
    switch (type) {
      case 'CASE_PAGING':
        this.projectFacade.updateCaseDetailRequestInfo({
          pageNumber,
          pageSize,
        })
        break
      case 'REVIEW_SET_PAGING':
        break
      case 'DOCUMENT_SHARE_PAGING':
        break
    }
  }

  /**
   * Fetches the case detail based on the case request info stored in the project facade.
   * @remarks
   *  The case request info is stored in state, and they are being updated before we fetch the case detail.
   * @returns {void}
   */
  #fetchCaseDetail(): void {
    this.projectFacade.fetchCaseDetail()
  }

  #fetchSharedDocsDetail(): void {
    this.sharedDocFacade.fetchSharedDocuments(-1, SharedDocRequestType.All)
  }

  /**
   * Handles the tab change effect.
   * @returns {void}
   */
  #handleTabChangeEffect(): void {
    effect(
      () => {
        // WARNING: do not invoke a lot of operations from here.
        // This is a signal that will be triggered whenever the tab changes.
        // We might end up in an infinite loop if we do not handle it properly.
        const tabTitle = this.selectedTabIndex()
        switch (tabTitle) {
          case LaunchpadTabTypes.CASE:
            this.#fetchCaseDetail()
            break
          case LaunchpadTabTypes.SHARED_DOCUMENT:
          case LaunchpadTabTypes.REVIEW_SET:
            break
        }
      },
      { injector: this.injector, allowSignalWrites: true }
    )
  }

  public fetchUnIndexMediaStatus(projectId: number): void {
    this.projectFacade.fetchUnIndexMediaStatus(projectId)
  }

  #notifyParentApp(content: unknown): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content,
      } as MessageContent,
    })
  }

  /**
   *
   * @param {number} projectId - projectid
   * @param {boolean} checkForParams - check for params in case the reprocessing window is opened from the email/in app notification
   * @returns {void}
   */
  private openReprocessWindow(
    projectId: number,
    isOpenFromEmail: boolean,
    settingId: number,
    token: string
  ): void {
    import(
      '../../reprocessing/case-reprocessing-container/case-reprocessing-container.component'
    ).then((td) => {
      const windowRef: WindowRef = this.windowService.open({
        content: td.CaseReprocessingContainerComponent, // Dynamically load main content
        left: 0,
        state: 'maximized',
        top: 100,
        cssClass: 'v-custom-window',
        titleBarContent: this.windowTitleBar(),
        resizable: false,
        draggable: false,
      })
      windowRef.content.setInput('projectId', projectId)
      windowRef.content.setInput('isOpenFromEmail', isOpenFromEmail)
      windowRef.content.setInput('settingId', settingId)
      windowRef.content.setInput('token', token)

      this.isReprocessWindowOpen.set(true)

      windowRef.result
        .pipe(
          filter((res) => !!res),
          take(1)
        )
        .subscribe(() => {
          this.isReprocessWindowOpen.set(false)
          windowRef.content.destroy()

          //send message to parent window to remove the query parameters so that
          //the reprocessing window does not open again on refresh and notification link works properly
          this.iframeMessengerService.sendMessage({
            type: 'MICRO_APP_DATA_CHANGE',
            eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
            eventTriggeredFor: 'PARENT_WINDOW',
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
            payload: {
              type: MessageType.ROUTE_CHANGE,
              content: {
                popup: 'reprocessing',
                removeParameters: true,
              },
            } as MessageContent,
          })
        })
    })
  }

  /**
   * Selects a tab manually in the Kendo TabStrip.
   * @param {TabStripTabComponent} selectedTab - The tab to be selected manually.
   * @returns {void}
   */
  #selectTabManually(selectedTab: TabStripTabComponent): void {
    if (!this.tabstrip() || !this.caseTab()) return

    const tabs = this.tabstrip()?.tabs.toArray()
    const tabIndex = tabs.indexOf(selectedTab)

    if (tabIndex > -1) {
      this.tabstrip().selectTab(tabIndex)
      const manualEvent = {
        index: tabIndex,
        title: this.caseTab().title,
        tab: this.caseTab,
        preventDefault: () => {},
      } as unknown as SelectEvent

      this.onSelect(manualEvent)
    }
  }

  /**
   * Handles the shared document tab view logic.
   * Automatically switches tabs based on the shared document count.
   * @returns {void}
   */
  #handleSharedDocTabView(): void {
    effect(
      () => {
        if (this.totalSharedDocs() === 0) {
          const selectedTab = !this.isReviewer()
            ? this.caseTab()
            : this.reviewTab()
          this.#selectTabManually(selectedTab)
        }
      },
      { injector: this.injector, allowSignalWrites: true }
    )
  }

  #handleReprocessNotification(): void {
    this.activatedRoute.queryParams
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((params) => {
        if (!this.isReprocessWindowOpen()) {
          if (params['popup'] === 'reprocessing') {
            this.projectToReprocess =
              this.activatedRoute.snapshot.queryParams['projectId']
            const isOpenFromEmail =
              this.activatedRoute.snapshot.queryParams['isFromEmail']
            const settingId =
              this.activatedRoute.snapshot.queryParams['settingId']
            const token = this.activatedRoute.snapshot.queryParams['token']

            if (this.projectToReprocess > 0) {
              this.openReprocessWindow(
                this.projectToReprocess,
                isOpenFromEmail,
                settingId,
                token
              )
            }
          }
        }
      })

    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'FRAME_WINDOW' &&
            (m.payload as MessageContent).content['popup'] === 'reprocessing' &&
            Boolean(
              (m.payload as MessageContent).content['loadReprocessing']
            ) &&
            (m.payload as MessageContent).type === MessageType.ROUTE_CHANGE
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestroy$)
      )
      .subscribe((params) => {
        if (!this.isReprocessWindowOpen()) {
          if (params.content['popup'] === 'reprocessing') {
            this.projectToReprocess = params.content['projectId']
            const isOpenFromEmail = params.content['isOpenFromEmail']
            const settingId = params.content['settingId']
            const token = params.content['token']

            if (this.projectToReprocess > 0) {
              this.openReprocessWindow(
                this.projectToReprocess,
                isOpenFromEmail,
                settingId,
                token
              )
            }
          }
        }
      })
  }

  /**
   * Select the parent notified message to open the notification dialog.
   * @returns {void}
   */
  #hanldeParentNotifiedMessage(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (message) =>
            message.eventTriggeredBy === AppIdentitiesTypes.VOD &&
            message.type === 'MICRO_APP_DATA_CHANGE' &&
            (message.payload as MessageContent).type ===
              MessageType.NOTIFY_CHANGE &&
            Boolean(
              message.payload?.['content']?.['caseNotificationMessage']
            ) &&
            message.eventTriggeredBy === AppIdentitiesTypes.VOD
        ),
        map((mc) => (mc.payload as MessageContent)?.content),
        takeUntil(this.toDestroy$)
      )
      .subscribe((result) => {
        const { message, resultType } = result
        if (this.unIndexMediaList()) {
          this.#openUnIndexMediaStatusDialog()
        } else {
          if (resultType === 'error') {
            this.#showNotificationMessage(message)
            return
          }
          this.#navigateToDocuments()
        }
      })
  }

  #navigateToDocuments(): void {
    this.projectFacade.resetProjectState([
      'unIndexMediaSuccessResponse',
      'isMediaStatusLoading',
    ])
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content: {
          shouldRedirect: true,
        },
      } as MessageContent,
    })
  }

  #handleProductionStatusNotification(): void {
    this.activatedRoute.queryParams
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe((params) => {
        if (!this.isProductionStatusDialogOpened()) {
          if (params['popup'] === 'production-status') {
            this.projectIdForProductionStatus =
              this.activatedRoute.snapshot.queryParams['projectId']

            if (this.projectIdForProductionStatus > 0) {
              this.#openProductionStatusDialog(
                this.projectIdForProductionStatus
              )
            }
          }
        }
      })

    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VENIO_NEXT &&
            m.eventTriggeredFor === 'FRAME_WINDOW' &&
            (m.payload as MessageContent).content['popup'] ===
              'production-status' &&
            (m.payload as MessageContent).type === MessageType.ROUTE_CHANGE
        ),
        map((message) => message.payload as MessageContent),
        takeUntil(this.toDestroy$)
      )
      .subscribe((params) => {
        if (!this.isProductionStatusDialogOpened()) {
          if (params.content['popup'] === 'production-status') {
            this.projectIdForProductionStatus = params.content['projectId']
            const isOpenFromEmail = params.content['isOpenFromEmail']

            if (this.projectIdForProductionStatus > 0) {
              this.#openProductionStatusDialog(
                this.projectIdForProductionStatus,
                isOpenFromEmail
              )
            }
          }
        }
      })
  }

  private showNotification(content: string, type: Type): void {
    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  /**
   * This function shows a notification message.
   * @param {string} content The content of the notification message.
   * @returns {void}
   */
  #showNotificationMessage(content = ''): void {
    const notificationDialogRef = this.dialogService.open({
      content: NotificationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-info',
      width: '25rem',
      appendTo: this.viewContainerRef,
    })

    // Set the dialog input
    this.#setDialogInput(notificationDialogRef.content.instance, content)
  }

  /**
   * This function sets the input for the dialog.
   * @param instance The instance of the ConfirmationDialogComponent.
   * @param content The content of the confrimation message.
   */

  #setDialogInput(instance: NotificationDialogComponent, content = ''): void {
    // Set the message of the dialog
    instance.title = 'Info'
    instance.message = content
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
