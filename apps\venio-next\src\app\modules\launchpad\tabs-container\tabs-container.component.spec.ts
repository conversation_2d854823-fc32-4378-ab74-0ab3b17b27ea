import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TabsContainerComponent } from './tabs-container.component'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideMockStore } from '@ngrx/store/testing'
import { EffectsModule } from '@ngrx/effects'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { StoreModule } from '@ngrx/store'
import {
  AppIdentitiesTypes,
  MESSAGE_SERVICE_CONFIG,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { environment } from '@venio/shared/environments'
import { ProjectFacade, UserFacade } from '@venio/data-access/common'
import { of } from 'rxjs'
import {
  DocumentShareFacade,
  RightModel,
  StartupsFacade,
} from '@venio/data-access/review'
import {
  CaseDetailResponseModel,
  ReviewSetSummary,
} from '@venio/shared/models/interfaces'
import { ActivatedRoute } from '@angular/router'
import { CommonActionTypes } from '@venio/shared/models/constants'

describe('TabsContainerComponent', () => {
  let component: TabsContainerComponent
  let fixture: ComponentFixture<TabsContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TabsContainerComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        provideNoopAnimations(),
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                popup: 'reprocessing',
                projectId: '1',
              },
            },
            queryParams: of({
              popup: 'reprocessing',
              projectId: '1',
            }),
          },
        },
        provideMockStore({
          initialState: {
            project: {
              isCaseDetailLoading: false,
            },
            usersStore: {
              currentUserSuccessResponse: {},
            },
          },
        }),
        { provide: WINDOW, useFactory: windowFactory, deps: [PLATFORM_ID] },
        {
          provide: MESSAGE_SERVICE_CONFIG,
          useValue: {
            origin: environment.allowedOrigin,
            iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
          },
        },
        {
          provide: ProjectFacade,
          useValue: {
            selectIsCaseDetailLoading$: of(false),
            selectCaseDetail$: of({
              caseDetailEntries: [],
            } as CaseDetailResponseModel),
            selectunIndexMediaSuccess$: of({
              status: 'SUCCESS',
              message: 'Data fetched successfully',
              data: [
                { custodianName: 'File A', mediaName: '', mediaStatus: '' },
                { custodianName: 'File B', mediaName: '', mediaStatus: '' },
              ],
            }),
            selectIsMediaStatusLoading$: of(false),
            updateCaseDetailRequestInfo: jest.fn(),
            fetchCaseDetail: jest.fn(),
            selectReviewSetSummaryDetail$: of({} as ReviewSetSummary),
            fetchReviewSetSummaryDetail: jest.fn(),
            fetchUnIndexMediaStatus: jest.fn(),
          } satisfies Partial<ProjectFacade>,
        },
        {
          provide: UserFacade,
          useValue: {
            selectCurrentUserDetails$: of({}),
            selectCurrentUserSuccessResponse$: of({}),
            selectCurrentUsername$: of(''),
          },
        },
        {
          provide: DocumentShareFacade,
          useValue: {
            selectSharedDocumentList$: of({}),
            fetchSharedDocuments: jest.fn(),
          },
        },
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({} as RightModel),
          } satisfies Partial<StartupsFacade>,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(TabsContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should fetch unindexed media status when the REVIEW action is triggered', () => {
    // GIVEN a projectId and a REVIEW action event
    const projectId = 123
    const mockEvent = {
      actionType: CommonActionTypes.REVIEW,
      content: { projectId },
    }

    // AND a spy on the fetchUnIndexMediaStatus method to track calls
    const fetchUnIndexMediaStatusSpy = jest.spyOn(
      component,
      'fetchUnIndexMediaStatus'
    )

    // WHEN the case grid action click handler is called with the REVIEW event
    component.caseGridActionClick(mockEvent)

    // THEN it should call fetchUnIndexMediaStatus with the correct projectId
    expect(fetchUnIndexMediaStatusSpy).toHaveBeenCalledWith(projectId)
  })

  it('should fetch unindexed media status when the ANALYZE action is triggered', () => {
    // GIVEN a projectId and a ANALYZE action event
    const projectId = 123
    const mockEvent = {
      actionType: CommonActionTypes.ANALYZE,
      content: { projectId },
    }

    // AND a spy on the fetchUnIndexMediaStatus method to track calls
    const fetchUnIndexMediaStatusSpy = jest.spyOn(
      component,
      'fetchUnIndexMediaStatus'
    )

    // WHEN the case grid action click handler is called with the ANALYZE event
    component.caseGridActionClick(mockEvent)

    // THEN it should call fetchUnIndexMediaStatus with the correct projectId
    expect(fetchUnIndexMediaStatusSpy).toHaveBeenCalledWith(projectId)
  })
})
