import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AdvancedOptionComponent } from './advanced-option.component'
import { ConvertToImageFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  TextBoxModule,
  NumericTextBoxModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { of } from 'rxjs'
describe('AdvancedOptionComponent', () => {
  let component: AdvancedOptionComponent
  let fixture: ComponentFixture<AdvancedOptionComponent>
  let convertToImageFacade: any

  beforeEach(async () => {
    convertToImageFacade = {
      getProjectPageLimit$: of(1000),
      getBatesStartNumber$: of(1),
      getCustomFields$: of([]),
      fetchImageCustomFields: jest.fn(),
    }
    await TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        LoaderModule,
        DropDownListModule,
        LayoutModule,
        LabelModule,
        TextBoxModule,
        NumericTextBoxModule,
        FormsModule,
        ReactiveFormsModule,
        GridModule,
      ],
      declarations: [AdvancedOptionComponent],
      providers: [
        { provide: ConvertToImageFacade, useValue: convertToImageFacade },
        provideMockStore({}),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AdvancedOptionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should disable customWidth, customHeight and dpi controls by default (when DEFAULT is selected)', () => {
    const group = component.imageForm.get('imageDimensionSettings')
    group.get('imageDimension').setValue('DEFAULT')
    fixture.detectChanges()

    expect(group.get('customWidth').disabled).toBe(true)
    expect(group.get('customHeight').disabled).toBe(true)
    expect(group.get('dpi').disabled).toBe(true)
  })

  it('should disable customWidth, customHeight and dpi controls when ORIGINAL is selected', () => {
    const group = component.imageForm.get('imageDimensionSettings')
    group.get('imageDimension').setValue('ORIGINAL')
    fixture.detectChanges()

    expect(group.get('customWidth').disabled).toBe(true)
    expect(group.get('customHeight').disabled).toBe(true)
    expect(group.get('dpi').disabled).toBe(true)
  })

  it('should enable customWidth, customHeight and dpi controls when CUSTOM is selected', () => {
    const group = component.imageForm.get('imageDimensionSettings')
    group.get('imageDimension').setValue('CUSTOM')
    fixture.detectChanges()

    expect(group.get('customWidth').enabled).toBe(true)
    expect(group.get('customHeight').enabled).toBe(true)
    expect(group.get('dpi').enabled).toBe(true)
  })

  it('should update form values for custom dimension inputs', () => {
    const group = component.imageForm.get('imageDimensionSettings')
    group.get('imageDimension').setValue('CUSTOM')
    group.get('customWidth').setValue(333)
    group.get('customHeight').setValue(444)
    group.get('dpi').setValue(300)
    fixture.detectChanges()

    expect(group.value).toEqual({
      imageDimension: 'CUSTOM',
      customWidth: 333,
      customHeight: 444,
      dpi: 300,
    })
  })
})
