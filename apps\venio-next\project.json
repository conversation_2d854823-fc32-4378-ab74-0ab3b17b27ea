{"name": "venio-next", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "venio", "sourceRoot": "apps/venio-next/src", "tags": ["venio-next"], "targets": {"build": {"executor": "@angular-devkit/build-angular:browser", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/venio-next/browser", "index": "apps/venio-next/src/index.html", "main": "apps/venio-next/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/venio-next/tsconfig.app.json", "webWorkerTsConfig": "tsconfig.venio-next-worker.json", "inlineStyleLanguage": "scss", "statsJson": true, "progress": true, "vendorChunk": true, "extractLicenses": true, "assets": ["apps/venio-next/src/assets", "apps/venio-next/src/manifest.webmanifest", {"glob": "**/*", "input": "./node_modules/pspdfkit/dist/pspdfkit-lib/", "output": "./assets/pspdfkit-lib/"}, {"glob": "**/*", "input": "libs/shared/assets/src/files/json/", "output": "./assets/"}], "styles": ["libs/shared/styles/src/rel-index.scss", "apps/venio-next/src/styles.scss"], "scripts": ["libs/shared/assets/src/files/js/AESEnc.js", "apps/venio-next/src/assets/js/watermark-removal.js"], "serviceWorker": true, "ngswConfigPath": "apps/venio-next/ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "2mb"}], "outputHashing": "all", "fileReplacements": [{"replace": "libs/shared/environments/src/lib/environment.ts", "with": "libs/shared/environments/src/lib/environment.prod.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"options": {"port": 4300}, "executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "venio-next:build:production"}, "development": {"buildTarget": "venio-next:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "venio-next:build"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "stylelint": {"executor": "nx:run-commands", "options": {"command": "npx stylelint \"apps/venio-next/**/*.scss\" --config .stylelintrc --fix --cache --cache-location=./stylelint-cache/style-lint-venio-next-cache\n"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/venio-next/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "venio-next:build", "port": 4300}}, "server": {"executor": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/venio-next/server", "main": "apps/venio-next/src/main.server.ts", "tsConfig": "apps/venio-next/tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media"}, "development": {"buildOptimizer": false, "optimization": false, "sourceMap": true, "extractLicenses": false}}, "defaultConfiguration": "production"}, "app-shell": {"executor": "@angular-devkit/build-angular:app-shell", "options": {"route": "shell"}, "configurations": {"production": {"browserTarget": "venio-next:build:production", "serverTarget": "venio-next:server:production"}, "development": {"browserTarget": "venio-next:build:development", "serverTarget": "venio-next:server:development"}}, "defaultConfiguration": "production"}, "e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "apps/venio-next/cypress.config.ts", "testingType": "e2e", "devServerTarget": "venio-next:serve"}, "configurations": {"production": {"devServerTarget": "venio-next:serve:production"}, "ci": {"devServerTarget": "venio-next:serve-static"}}}}}