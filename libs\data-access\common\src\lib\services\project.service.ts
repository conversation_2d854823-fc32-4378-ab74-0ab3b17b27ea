import { HttpClient, HttpParams } from '@angular/common/http'
import { environment } from '@venio/shared/environments'
import { Injectable } from '@angular/core'
import { delay, Observable } from 'rxjs'
import {
  CaseDetailRequestInfo,
  ResponseModel,
  ReviewSetBatchRequestModel,
  ReviewSetDetailRequestModel,
} from '@venio/shared/models/interfaces'
import { CaseModel } from '@venio/data-access/review'

@Injectable({ providedIn: 'root' })
export class ProjectService {
  constructor(private http: HttpClient) {}

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  public fetchProjectGroup(projectId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/Group`
    )
  }

  public fetchProjectFolderTreeWithCount(
    projectId: number
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/${projectId}/folder-tree-with-count`,
      {}
    )
  }

  public fetchSearchOptions(projectId: number): Observable<number> {
    return this.http.get<number>(
      `${this._apiUrl}cases/project/${projectId}/search-option`
    )
  }

  public fetchProjects(): Observable<CaseModel[]> {
    return this.http.get<CaseModel[]>(
      `${this._apiUrl}cases?sortby=projectId&desc=true`
    )
  }

  public fetchProjectInfo(projectId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}cases/project/${projectId}/case-info`
    )
  }

  public fetchCaseDetail(
    pagingInfo: CaseDetailRequestInfo
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}cases/case-detail`,
      pagingInfo
    )
  }

  public fetchReviewSetSummaryDetail(
    requestModel: ReviewSetDetailRequestModel
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/reviewset/summary`,
      requestModel
    )
  }

  public deleteReviewSet(
    projectId: number,
    reviewSetId: number
  ): Observable<ResponseModel> {
    const url = `${this._apiUrl}project/${projectId}/reviewset/${reviewSetId}/purpose/delete`
    return this.http.delete<ResponseModel>(url)
  }

  public toggleFavoriteProject(
    projectId: number,
    isFavorite: boolean
  ): Observable<ResponseModel> {
    // If the payload properties are increasing, then the arg should be an object, i.e., payload:{ many props..}
    return this.http
      .post<ResponseModel>(
        `${this._apiUrl}user/favorite/${isFavorite ? 'update' : 'remove'}`,
        { projectId }
      )
      .pipe(delay(100))
  }

  public fetchProjectRights(projectIds: number[]): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}right/project-user-rights`,
      {
        projectIds,
      }
    )
  }

  public fetchTaggedDocumentTags(
    projectId: number,
    reviewSetId: number
  ): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}project/${projectId}/reviewset/${reviewSetId}/tagged-document`
    )
  }

  public fetchReviewSetDocumentView(
    projectId: number,
    reviewSetId: number,
    payload: {
      searchResultTempTable: string
    }
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/${projectId}/reviewsets/${reviewSetId}/document-view`,
      payload
    )
  }

  public fetchReviewSetBatch(
    projectId: number,
    reviewSetId: number,
    requestModel: ReviewSetBatchRequestModel
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/${projectId}/reviewset/${reviewSetId}/batch`,
      requestModel
    )
  }

  public fetchReviewSetUserAndUserGroups(
    projectId: number,
    reviewSetId: number
  ): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}user/project/${projectId}/reviewset/${reviewSetId}`
    )
  }

  public reassignBatch(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    reviewerId: number
  ): Observable<ResponseModel> {
    const url =
      this._apiUrl +
      'project/' +
      projectId +
      '/reviewset/' +
      reviewSetId +
      '/batch/' +
      batchId +
      '/user/' +
      reviewerId
    return this.http.put<ResponseModel>(url, {})
  }

  public purgeBatches(
    projectId: number,
    reviewSetId: number,
    batchIds: number[]
  ): Observable<ResponseModel> {
    return this.http.post<ResponseModel>(
      `${this._apiUrl}project/${projectId}/reviewset/${reviewSetId}/batch/purge`,
      batchIds
    )
  }

  public deleteBatches(
    projectId: number,
    reviewSetId: number,
    batchIds: number[]
  ): Observable<ResponseModel> {
    return this.http.delete<ResponseModel>(
      `${this._apiUrl}project/${projectId}/reviewset/${reviewSetId}/batch`,
      {
        body: batchIds,
      }
    )
  }

  public checkUploadInvitationAccess(
    projectId: number,
    userWiseToken: string
  ): Observable<ResponseModel> {
    const params = new HttpParams().set('userWiseToken', userWiseToken)
    return this.http.get<ResponseModel>(
      `${this._apiUrl}Upload/project/${projectId}/invitation-handle`,
      { params }
    )
  }

  public fetchUnIndexMedia(projectId: number): Observable<ResponseModel> {
    return this.http.get<ResponseModel>(
      `${this._apiUrl}/media/project/${projectId}/unindexed-media`
    )
  }
}
