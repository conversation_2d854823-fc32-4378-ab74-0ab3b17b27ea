import { createAction, props } from '@ngrx/store'
import {
  CaseDetailResponseModel,
  CaseDetailRequestInfo,
  ResponseModel,
  ReviewSetSummary,
  ReviewSetEntry,
  ReviewSetBatchRequestModel,
  ReviewSetBatchModel,
  UserGroupTree,
} from '@venio/shared/models/interfaces'
import { ProjectState } from './project.reducer'
import { CaseModel, UserRights } from '@venio/data-access/review'

export enum ProjectActionTypes {
  // Actions for Fetch Project Group
  FetchProjectGroup = '[Project] Fetch Project Group',
  FetchProjectGroupSuccess = '[Project] Fetch Project Group: Success',
  FetchProjectGroupFailure = '[Project] Fetch Project Group: Failure',

  // Actions for fetch projects
  FetchProjects = '[Project] Fetch Projects',
  FetchProjectsSuccess = '[Project] Fetch Projects: Success',
  FetchProjectsFailure = '[Project] Fetch Projects: Failure',

  // Action for fetch project info
  FetchProjectInfo = '[Project] Fetch Project Info',
  FetchProjectInfoSuccess = '[Project] Fetch Project Info: Success',
  FetchProjectInfoFailure = '[Project] Fetch Project Info: Failure',

  // Actions for fetch project folders
  FetchProjectFolderTreeWithCount = '[Project] Fetch Project Folder Tree With Count',
  FetchProjectFolderTreeWithCountSuccess = '[Project] Fetch Project Folder Tree With Count: Success',
  FetchProjectFolderTreeWithCountFailure = '[Project] Fetch Project Folder Tree With Count: Failure',

  // Actions for fetch search options for a project
  FetchSearchOptions = '[Project] Fetch Project Search Options',
  FetchSearchOptionsSuccess = '[Project] Fetch Project Search Options: Success',
  FetchSearchOptionsFailure = '[Project] Fetch Project Search Options: Failure',

  // Actions for fetch case details
  FetchCaseDetail = '[Project] Fetch Case Details',
  FetchCaseDetailSuccess = '[Project] Fetch Case Details: Success',
  FetchCaseDetailFailure = '[Project] Fetch Case Details: Failure',
  CaseDetailRequestUpdate = '[Project] Case Detail Request Update',
  StoreSelectedCaseDetail = '[Project] Store Selected Case Detail',

  // Actions for fetch review set summary details
  FetchReviewSetSummaryDetail = '[Project] Fetch Review Set Summary Details',
  FetchReviewSetSummaryDetailSuccess = '[Project] Fetch Review Set Summary Details: Success',
  FetchReviewSetSummaryDetailFailure = '[Project] Fetch Review Set Summary Details: Failure',
  ReviewSetSummaryDetailRequestUpdate = '[Project] Review Set Summary Detail Request Update',
  StoreSelectedReviewSetSummaryDetail = '[Project] Store Selected Review Set Summary Detail',

  // Actions for deleting review set
  DeleteReviewSet = '[Project] Delete Review Set',
  DeleteReviewSetSuccess = '[Project] Delete Review Set: Success',
  DeleteReviewSetFailure = '[Project] Delete Review Set: Failure',

  // Actions for toggling favorite project
  ToggleFavoriteProject = '[Project] Toggle Favorite Project',
  ToggleFavoriteProjectSuccess = '[Project] Toggle Favorite Project: Success',
  ToggleFavoriteProjectFailure = '[Project] Toggle Favorite Project: Failure',

  // Actions for fetching project rights
  FetchProjectRights = '[Project] Fetch Project Rights',
  FetchProjectRightsSuccess = '[Project] Fetch Project Rights: Success',
  FetchProjectRightsFailure = '[Project] Fetch Project Rights: Failure',

  // Action for fetching tagged document
  FetchTaggedDocumentTags = '[Project] Fetch Tagged Document Tags',
  FetchTaggedDocumentTagsSuccess = '[Project] Fetch Tagged Document Tags: Success',
  FetchTaggedDocumentTagsFailure = '[Project] Fetch Tagged Document Tags: Failure',

  // Actions for review set document view documents
  UpdateFetchReviewSetDocumentViewLoader = '[Project] Update Fetch Review Set Document View Loader',
  FetchReviewSetDocumentView = '[Project] Fetch Review Set Document View',
  FetchReviewSetDocumentViewSuccess = '[Project] Fetch Review Set Document View: Success',
  FetchReviewSetDocumentViewFailure = '[Project] Fetch Review Set Document View: Failure',

  // Resetting Project State
  ResetProjectState = '[Project] Reset State',

  // Actions for fetching reviewset batch details
  FetchReviewSetBatch = '[Project] Fetch Review Set Batch',
  FetchReviewSetBatchSuccess = '[Project] Fetch ReviewSet Batch: Success',
  FetchReviewSetBatchFailure = '[Project] Fetch ReviewSet Batch: Failure',
  ReviewSetBatchRequestUpdate = '[Project] Review Set Batch Request Update',
  SetSelectedBatchDetail = '[Project] Set Selected Batch Detail',

  FetchReviewSetUserGroups = '[Project] Fetch Review Set User Groups',
  FetchReviewSetUserGroupsSuccess = '[Project] Fetch Review Set User Groups: Success',
  FetchReviewSetUserGroupsFailure = '[Project] Fetch Review Set User Groups: Failure',

  // Actions for reassign review set
  ReassignReviewSetBatch = '[Project] Reassign Review Set Batch',
  ReassignReviewSetBatchSuccess = '[Project] Reassign Review Set Batch: Success',
  ReassignReviewSetBatchFailure = '[Project] Reassign Review Set Batch: Failure',

  // Actions for reassign review set
  RebatchReviewSetBatch = '[Project] Rebatch Review Set Batch',
  RebatchReviewSetBatchSuccess = '[Project] Rebatch Review Set Batch: Success',
  RebatchReviewSetBatchFailure = '[Project] Rebatch Review Set Batch: Failure',

  // Actions for deleting review set batch
  DeleteReviewSetBatch = '[Project] Delete Review Set Batch',
  DeleteReviewSetBatchSuccess = '[Project] Delete Review Set Batch: Success',
  DeleteReviewSetBatchFailure = '[Project] Delete Review Set Batch: Failure',

  // Actions for checking access for upload invitation
  CheckUploadInvitationAccess = '[Project] Check Upload Invitation Access',
  CheckUploadInvitationAccessSuccess = '[Project] Check Upload Invitation Access: Success',
  CheckUploadInvitationAccessFailure = '[Project] Check Upload Invitation Access: Failure',

  // Actions for Fetch Project Media Status
  FetchUnIndexMediaStatus = '[Project] Fetch Project Media Status',
  FetchUnIndexMediaStatusSuccess = '[Project] Fetch Project Media Status: Success',
  FetchUnIndexMediaStatusFailure = '[Project] Fetch Project Media Status: Failure',
}

export const resetProjectState = createAction(
  ProjectActionTypes.ResetProjectState,
  props<{ stateKey: keyof ProjectState | Array<keyof ProjectState> }>()
)

export const fetchProjectGroup = createAction(
  ProjectActionTypes.FetchProjectGroup,
  props<{ projectId: number }>()
)

export const fetchProjectGroupFailure = createAction(
  ProjectActionTypes.FetchProjectGroupFailure,
  props<{ projectGroupErrorResponse: ResponseModel }>()
)

export const fetchProjectGroupSuccess = createAction(
  ProjectActionTypes.FetchProjectGroupSuccess,
  props<{ projectGroupSuccessResponse: ResponseModel }>()
)

export const fetchProjectFolderTreeWithCount = createAction(
  ProjectActionTypes.FetchProjectFolderTreeWithCount,
  props<{ projectId: number }>()
)

export const fetchProjectFolderTreeWithCountFailure = createAction(
  ProjectActionTypes.FetchProjectFolderTreeWithCountFailure,
  props<{ projectFolderTreeWithCountErrorResponse: ResponseModel }>()
)

export const fetchProjectFolderTreeWithCountSuccess = createAction(
  ProjectActionTypes.FetchProjectFolderTreeWithCountSuccess,
  props<{ projectFolderTreeWithCountSuccessResponse: ResponseModel }>()
)

export const fetchSearchOptions = createAction(
  ProjectActionTypes.FetchSearchOptions,
  props<{ projectId: number }>()
)

export const fetchSearchOptionsFailure = createAction(
  ProjectActionTypes.FetchSearchOptionsFailure,
  props<{ searchOptionErrorResponse: ResponseModel }>()
)

export const fetchSearchOptionsSuccess = createAction(
  ProjectActionTypes.FetchSearchOptionsSuccess,
  props<{ searchOptionSuccessResponse: number }>()
)

export const fetchProjects = createAction(ProjectActionTypes.FetchProjects)

export const fetchProjectsSuccess = createAction(
  ProjectActionTypes.FetchProjectsSuccess,
  props<{ fetchProjectSuccessResponse: CaseModel[] }>()
)

export const fetchProjectsFailure = createAction(
  ProjectActionTypes.FetchProjectsFailure,
  // type ANY due to legacy API
  props<{ fetchProjectErrorResponse: any }>()
)

export const fetchProjectInfo = createAction(
  ProjectActionTypes.FetchProjectInfo,
  props<{ projectId: number }>()
)

export const fetchProjectInfoFailure = createAction(
  ProjectActionTypes.FetchProjectInfoFailure,
  props<{ projectInfoErrorResponse: ResponseModel }>()
)

export const fetchProjectInfoSuccess = createAction(
  ProjectActionTypes.FetchProjectInfoSuccess,
  props<{ projectInfoSuccessResponse: ResponseModel }>()
)

export const fetchCaseDetail = createAction(ProjectActionTypes.FetchCaseDetail)

export const fetchCaseDetailSuccess = createAction(
  ProjectActionTypes.FetchCaseDetailSuccess,
  props<{ caseDetailSuccessResponse: CaseDetailResponseModel }>()
)

export const fetchCaseDetailFailure = createAction(
  ProjectActionTypes.FetchCaseDetailFailure,
  props<{ caseDetailErrorResponse: ResponseModel }>()
)

export const updateCaseDetailRequestInfo = createAction(
  ProjectActionTypes.CaseDetailRequestUpdate,
  props<{ caseDetailRequestInfo: Partial<CaseDetailRequestInfo> }>()
)

export const storeSelectedCaseDetail = createAction(
  ProjectActionTypes.StoreSelectedCaseDetail,
  props<{ selectedCaseIds: number[] }>()
)

export const toggleFavoriteProject = createAction(
  ProjectActionTypes.ToggleFavoriteProject,
  props<{ projectId: number; isFavoriteProject: boolean }>()
)

export const toggleFavoriteProjectSuccess = createAction(
  ProjectActionTypes.ToggleFavoriteProjectSuccess,
  props<{ projectId: number; isFavoriteProject: boolean }>()
)

export const toggleFavoriteProjectFailure = createAction(
  ProjectActionTypes.ToggleFavoriteProjectFailure,
  props<{ projectId: number }>()
)

export const fetchProjectRights = createAction(
  ProjectActionTypes.FetchProjectRights,
  props<{ projectIds: number[] }>()
)

export const fetchProjectRightsSuccess = createAction(
  ProjectActionTypes.FetchProjectRightsSuccess,
  props<{ projectRights: Record<UserRights, number[]> }>()
)

export const fetchProjectRightsFailure = createAction(
  ProjectActionTypes.FetchProjectRightsFailure,
  props<{ projectRightErrorResponse: ResponseModel }>()
)

export const fetchReviewSetSummaryDetail = createAction(
  ProjectActionTypes.FetchReviewSetSummaryDetail
)

export const fetchReviewSetSummaryDetailSuccess = createAction(
  ProjectActionTypes.FetchReviewSetSummaryDetailSuccess,
  props<{ reviewSetSummaryDetailSuccess: ReviewSetSummary }>()
)

export const fetchReviewSetSummaryDetailFailure = createAction(
  ProjectActionTypes.FetchReviewSetSummaryDetailFailure,
  props<{ reviewSetSummaryDetailError: ResponseModel }>()
)

export const updateReviewSetSummaryDetailRequestInfo = createAction(
  ProjectActionTypes.ReviewSetSummaryDetailRequestUpdate,
  props<{ reviewSetSummaryDetailRequestInfo: Partial<CaseDetailRequestInfo> }>()
)

export const storeSelectedReviewSetSummaryDetail = createAction(
  ProjectActionTypes.StoreSelectedReviewSetSummaryDetail,
  props<{ selectedReviewSetSummaryDetail: ReviewSetEntry[] }>()
)

export const deleteReviewSet = createAction(
  ProjectActionTypes.DeleteReviewSet,
  props<{ projectId: number; reviewSetId: number }>()
)

export const deleteReviewSetSuccess = createAction(
  ProjectActionTypes.DeleteReviewSetSuccess,
  props<{ reviewSetDeleteSuccess: ResponseModel }>()
)

export const deleteReviewSetFailure = createAction(
  ProjectActionTypes.DeleteReviewSetFailure,
  props<{ reviewSetDeleteError: ResponseModel }>()
)

export const fetchTaggedDocumentTags = createAction(
  ProjectActionTypes.FetchTaggedDocumentTags,
  props<{ projectId: number; reviewSetId: number }>()
)

export const fetchTaggedDocumentTagsSuccess = createAction(
  ProjectActionTypes.FetchTaggedDocumentTagsSuccess,
  props<{ taggedDocumentTagsSuccess: ResponseModel }>()
)

export const fetchTaggedDocumentTagsFailure = createAction(
  ProjectActionTypes.FetchTaggedDocumentTagsFailure,
  props<{ taggedDocumentTagsError: ResponseModel }>()
)

export const updateReviewSetDocumentViewLoader = createAction(
  ProjectActionTypes.UpdateFetchReviewSetDocumentViewLoader,
  props<{ isReviewSetDocumentViewLoading: boolean | undefined }>()
)

export const fetchReviewSetDocumentView = createAction(
  ProjectActionTypes.FetchReviewSetDocumentView,
  props<{
    projectId: number
    reviewSetId: number
    payload: {
      searchResultTempTable: string
    }
  }>()
)

export const fetchReviewSetDocumentViewSuccess = createAction(
  ProjectActionTypes.FetchReviewSetDocumentViewSuccess,
  props<{ reviewSetDocumentViewSuccess: ResponseModel }>()
)

export const fetchReviewSetDocumentViewFailure = createAction(
  ProjectActionTypes.FetchReviewSetDocumentViewFailure,
  props<{ reviewSetDocumentViewError: ResponseModel }>()
)

export const fetchReviewSetBatch = createAction(
  ProjectActionTypes.FetchReviewSetBatch,
  props<{
    projectId: number
    reviewSetId: number
  }>()
)

export const fetchReviewSetBatchSuccess = createAction(
  ProjectActionTypes.FetchReviewSetBatchSuccess,
  props<{ reviewSetBatchSuccess: ReviewSetBatchModel[] }>()
)

export const fetchReviewSetBatchFailure = createAction(
  ProjectActionTypes.FetchReviewSetBatchFailure,
  props<{ reviewSetBatchError: ResponseModel }>()
)

export const updateReviewSetBatchRequestInfo = createAction(
  ProjectActionTypes.ReviewSetBatchRequestUpdate,
  props<{ reviewSetBatchRequestInfo: Partial<ReviewSetBatchRequestModel> }>()
)

export const setSelectedBatchDetail = createAction(
  ProjectActionTypes.SetSelectedBatchDetail,
  props<{ selectedBatchDetail: ReviewSetBatchModel[] }>()
)

export const fetchReviewSetUserGroups = createAction(
  ProjectActionTypes.FetchReviewSetUserGroups,
  props<{
    projectId: number
    reviewSetId: number
  }>()
)

export const fetchReviewSetUserGroupsSuccess = createAction(
  ProjectActionTypes.FetchReviewSetUserGroupsSuccess,
  props<{ reviewSetUserGroupSuccess: UserGroupTree[] }>()
)

export const fetchReviewSetUserGroupsFailure = createAction(
  ProjectActionTypes.FetchReviewSetUserGroupsFailure,
  props<{ reviewSetUserGroupError: ResponseModel }>()
)

export const reassignReviewSetBatch = createAction(
  ProjectActionTypes.ReassignReviewSetBatch,
  props<{
    projectId: number
    reviewSetId: number
    batchId: number
    reviewerId: number
  }>()
)

export const reassignReviewSetBatchSuccess = createAction(
  ProjectActionTypes.ReassignReviewSetBatchSuccess,
  props<{ reviewSetReassignSuccess: ResponseModel }>()
)

export const reassignReviewSetBatchFailure = createAction(
  ProjectActionTypes.ReassignReviewSetBatchFailure,
  props<{ reviewSetReassignError: ResponseModel }>()
)

export const rebatchReviewSetBatch = createAction(
  ProjectActionTypes.RebatchReviewSetBatch,
  props<{ projectId: number; reviewSetId: number; batchIds: number[] }>()
)

export const rebatchReviewSetBatchSuccess = createAction(
  ProjectActionTypes.RebatchReviewSetBatchSuccess,
  props<{ reviewSetRebatchSuccess: ResponseModel }>()
)

export const rebatchReviewSetBatchFailure = createAction(
  ProjectActionTypes.RebatchReviewSetBatchFailure,
  props<{ reviewSetReBatchError: ResponseModel }>()
)

export const deleteReviewSetBatch = createAction(
  ProjectActionTypes.DeleteReviewSetBatch,
  props<{
    projectId: number
    reviewSetId: number
    batchIds: number[]
  }>()
)

export const deleteReviewSetBatchSuccess = createAction(
  ProjectActionTypes.DeleteReviewSetBatchSuccess,
  props<{ reviewSetDeleteBatchSuccess: ResponseModel }>()
)

export const deleteReviewSetBatchFailure = createAction(
  ProjectActionTypes.DeleteReviewSetBatchFailure,
  props<{ reviewSetDeleteBatchError: ResponseModel }>()
)

export const checkUploadInvitationAccess = createAction(
  ProjectActionTypes.CheckUploadInvitationAccess,
  props<{ projectId: number; userWiseToken: string }>()
)

export const checkUploadInvitationAccessSuccess = createAction(
  ProjectActionTypes.CheckUploadInvitationAccessSuccess,
  props<{ response: ResponseModel }>()
)

export const fetchUnIndexMediaStatus = createAction(
  ProjectActionTypes.FetchUnIndexMediaStatus,
  props<{ projectId: number }>()
)

export const fetchUnIndexMediaStatusSuccess = createAction(
  ProjectActionTypes.FetchUnIndexMediaStatusSuccess,
  props<{ unIndexMediaSuccessResponse: ResponseModel }>()
)

export const fetchUnIndexMediaStatusFailure = createAction(
  ProjectActionTypes.FetchUnIndexMediaStatusFailure,
  props<{ unIndexMediaErrorResponse: ResponseModel }>()
)
