import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  Input,
  OnDestroy,
  OnInit,
  signal,
  Type,
  ViewContainerRef,
} from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  NotificationRef,
  NotificationService,
} from '@progress/kendo-angular-notification'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { DataAccessCommonModule, TagsFacade } from '@venio/data-access/common'
import {
  DocumentProjectTagRequestModel,
  DocumentTag,
  DocumentTagFacade,
  DocumentTagPayloadModel,
  ReviewPanelFacade,
  TagActionType,
  ReviewPanelViewState,
  TagGroupInfo,
  DocumentProjectTagGroupModel,
} from '@venio/data-access/document-utility'
import {
  DocumentsFacade,
  ReviewFacade,
  SearchFacade,
  StartupsFacade,
  UserRights,
} from '@venio/data-access/review'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import {
  DocumentTagTreeListToggle,
  ShortcutKeyActions,
  TagGroupActionTitle,
  TagGroupActionType,
} from '@venio/shared/models/constants'
import { LocalStorage } from '@venio/shared/storage'
import { cloneDeep, isEqual, uniq } from 'lodash'
import {
  Subject,
  combineLatest,
  distinctUntilChanged,
  filter,
  takeUntil,
} from 'rxjs'

import { moreHorizontalIcon } from '@progress/kendo-svg-icons'
import { toSignal } from '@angular/core/rxjs-interop'
@Component({
  selector: 'venio-document-tag',
  templateUrl: './document-tag.component.html',
  styleUrls: ['./document-tag.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SvgLoaderDirective,
    TooltipsModule,
    InputsModule,
    ButtonsModule,
    IconsModule,
    LoaderModule,
    DataAccessCommonModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentTagComponent implements OnInit, OnDestroy {
  public tagTreeWrapperComponent = import(
    '../tags-tree-wrapper/tags-tree-wrapper.component'
  ).then((c) => c.TagsTreeWrapperComponent)

  public treeNodes: any[]

  public initialClonedTreeNodes: any[]

  public initialClonedDocumentTags: DocumentTag[]

  public clonedDocumentTags: any[]

  public documentTags: any[]

  public filteredTags: any[]

  public expandedKeys: number[] = []

  public expandedClonedKeys: number[] = []

  private expandedTagIds: number[] = []

  private newTagExpandedIds: number[] = []

  private newTagId: number | undefined

  private currentDocument: number

  private selectedDocuments: number[]

  private isBulkDocument = false

  private isBatchSelected = false

  private searchTempTable = ''

  private searchTag = ''

  public isComponentReady = false

  public isTagListExpanded = false

  public readonly isUserAndSystemTagGroup = computed(
    () =>
      this.reviewPanelViewState.selectedTagGroup() ===
      TagGroupActionType.USER_SYSTEM
  )

  public readonly canShowCopyTags = signal(false)

  public readonly copyDocumentTags = this.reviewPanelViewState.copyDocumentTags

  public readonly isAITagExists = this.reviewPanelViewState.isAITagExists

  public readonly selectedTagGroup = this.reviewPanelViewState.selectedTagGroup

  public readonly showExpandCollapse = signal(true)

  private readonly aiTagGroup: string[] = [
    TagGroupActionType.RELEVANCE,
    TagGroupActionType.PRIVILEDE,
  ]

  public icons = {
    dotsIcon: moreHorizontalIcon,
  }

  private notificationRef: NotificationRef

  public get isReviewPanelPopout(): boolean {
    return LocalStorage.get<boolean>('isReviewPanelPopout')
  }

  @Input()
  public tagActionType: TagActionType

  public tagTreeWrapperComponentItem: { component: any; inputs: any }

  public svgIconForTagListToggleControls = [
    {
      actionType: DocumentTagTreeListToggle.EXPAND_ALL,
      actionText: DocumentTagTreeListToggle.EXPAND_ALL,
      iconPath: 'assets/svg/plus-circle-expand-icon-svg.svg',
      iconColor: '#9BD2A7',
      hoverColor: '#FFBB12',
    },
    {
      actionType: DocumentTagTreeListToggle.COLLAPSE_ALL,
      actionText: DocumentTagTreeListToggle.COLLAPSE_ALL,
      iconPath: 'assets/svg/minus-circle-collapse-svg.svg',
      iconColor: '#ED7425',
      hoverColor: '#FFBB12',
    },
  ]

  public tagGroupActions = [
    {
      title: TagGroupActionTitle.USER_SYSTEM,
      actionType: TagGroupActionType.USER_SYSTEM,
    },
    {
      title: TagGroupActionTitle.RELEVANCE,
      actionType: TagGroupActionType.RELEVANCE,
    },
    {
      title: TagGroupActionTitle.PRIVILEDE,
      actionType: TagGroupActionType.PRIVILEDE,
    },
    {
      title: TagGroupActionTitle.PII,
      actionType: TagGroupActionType.PII,
    },
  ]

  public tagGroupHeaderClass = "before:t-content-['Select___Tags']"

  public isDocumentTagsLoading$ = this.documentTagFacade.isDocumentTagLoading$

  private unsubscribed$: Subject<void> = new Subject<void>()

  @Input()
  public isConflictDialog: boolean

  constructor(
    private searchFacade: SearchFacade,
    private documentTagFacade: DocumentTagFacade,
    private documentsFacade: DocumentsFacade,
    private tagsFacade: TagsFacade,
    private reviewFacade: ReviewFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private reviewPanelViewState: ReviewPanelViewState,
    private notificationService: NotificationService,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private vcr: ViewContainerRef,
    private startupsFacade: StartupsFacade
  ) {}

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.selectFilterDocumentTags()
    this.selectExpandedTagIds()
    this.fetchTagRuleDescription()
    this.fetchTagRuleList()
    this.fetchProjectTags()
    this.fetchTagSettings()
    this.selectProjectTags()
    this.#selectDocumentProjectTagGroup()
    this.selectTagIds()
    this.selectDocumentTags()
    this.#selectShowTagRuleList()
    this.selectFilteredProjectTags()
    this.#selectTagReOrderResponses()
    this.#selectReloadTagGroupData()
    this.#selectTagAddOrUpdateResponses()
    this.#selectApplyTagErrorResponses()
    this.#selectIsDocumentTagUpdated()
    this.#selectShortcutKeysAction()
  }

  public fetchProjectTags(): void {
    this.documentTagFacade.fetchProjectTags(this.projectId, false, false)
  }

  public selectProjectTags(): void {
    this.documentTagFacade.selectProjectTags$
      .pipe(
        filter((tags) => tags?.length > 0),
        distinctUntilChanged(),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tags) => {
        if (this.filteredTags?.length > 0) {
          this.treeNodes = this.filteredTags
        } else {
          this.treeNodes = tags
        }
        this.initialClonedTreeNodes = cloneDeep(this.treeNodes)
        this.#expandTagTreeIfNewTagAdded(this.treeNodes)
        this.getDocumentTags()
        this.changeDetectorRef.markForCheck()
      })
  }

  public fetchTagRuleList(): void {
    this.documentTagFacade.fetchTagRuleList(this.projectId)
  }

  public fetchTagRuleDescription(): void {
    this.documentTagFacade.fetchTagRuleDescription(this.projectId)
  }

  public selectTagIds(): void {
    this.documentTagFacade.selectFlatProjectTags$
      .pipe(
        filter((tags) => tags?.length > 0),
        distinctUntilChanged(),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((tags) => {
        this.expandedClonedKeys = tags.map((tag) => tag.TagId)
        this.expandedKeys = this.expandedClonedKeys.filter((item) =>
          this.expandedTagIds.includes(item)
        )
        this.#checkIfNewTagAdded()
        this.changeDetectorRef.markForCheck()
      })
  }

  public selectExpandedTagIds(): void {
    this.documentTagFacade.selectExpandedTagIds$
      .pipe(
        filter((expandedTagIds) => expandedTagIds?.length > 0),
        distinctUntilChanged(),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((expandedTagIds) => {
        this.expandedTagIds = expandedTagIds
        if (this.expandedTagIds?.length > 0) {
          this.isTagListExpanded = true
          this.expandedKeys = this.expandedClonedKeys?.filter((item) =>
            this.expandedTagIds.includes(item)
          )
          this.changeDetectorRef.markForCheck()
        }
      })
  }

  public selectFilteredProjectTags(): void {
    this.documentTagFacade.selectFilteredProjectTags$
      .pipe(
        filter((filteredTags) => Boolean(filteredTags)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((filteredTags) => {
        this.treeNodes = this.filteredTags = filteredTags
        this.initialClonedTreeNodes = cloneDeep(this.treeNodes)
        this.loadTagTreeWrapperComponent()
        this.changeDetectorRef.markForCheck()
      })
  }

  private selectFilterDocumentTags(): void {
    this.documentTagFacade.selectFilterDocumentTags$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((search) => {
        this.searchTag = search
      })
  }

  #selectShortcutKeysAction(): void {
    this.reviewPanelFacade.getShortcutKeysAction
      .pipe(
        filter((action) => action === ShortcutKeyActions.UNDO_TAG_CODING),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.clonedDocumentTags = cloneDeep(this.initialClonedDocumentTags)
        this.loadTagTreeWrapperComponent()
      })
  }

  public getDocumentTags(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.searchFacade.getSearchTempTables$,
      this.documentsFacade.isBulkDocument$,
      this.documentsFacade.getIsBatchSelected$,
      this.reviewFacade.getInclusiveEmailResponseModel$,
    ])
      .pipe(
        filter(
          ([selectedDocuments, tempTables]) =>
            this.tagActionType === TagActionType.TagAllInclusiveEmails ||
            Boolean(
              selectedDocuments &&
                selectedDocuments.length > 0 &&
                !isEqual(selectedDocuments, this.selectedDocuments) &&
                tempTables
            )
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(
        ([
          selectedDocuments,
          tempTables,
          isBulkDocument,
          isBatchSelected,
          invlusiveEmailModel,
        ]) => {
          this.selectedDocuments = selectedDocuments
          if (this.tagActionType === TagActionType.TagAllInclusiveEmails) {
            this.searchTempTable =
              invlusiveEmailModel?.inclusiveEmailTempTable ??
              tempTables?.searchResultTempTable
          } else if (
            this.tagActionType ===
            TagActionType.TagWholeThreadOfSelectedDocuments
          ) {
            this.searchTempTable = tempTables?.viewTypeSearchResultTempTable
          } else {
            this.searchTempTable = tempTables?.searchResultTempTable
          }
          this.isBulkDocument = isBulkDocument
          this.currentDocument = this.selectedDocuments[0]
          this.isBatchSelected = isBatchSelected
          this.canShowCopyTags.set(!this.isBulkDocument)
          // The "Document Projet Tag Group" is not required if it's a bulk document.
          if (this.isBulkDocument) {
            this.reviewPanelViewState.setIsAITagExists(false)
            this.fetchDocumentTags()
          } else {
            this.fetchDocumentProjectTagGroup()
          }
        }
      )
  }

  public allowToCopyTag = toSignal(
    this.startupsFacade.hasGroupRight$(UserRights.ALLOW_TO_COPY_TAG)
  )

  #selectDocumentProjectTagGroup(): void {
    this.documentTagFacade.selectDocumentProjectTagGroup$
      .pipe(
        filter((documentProjectTagGroup) => Boolean(documentProjectTagGroup)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((documentProjectTagGroup) => {
        this.fetchDocumentTags()
        this.#handleDocumentTagGroups(documentProjectTagGroup)
      })
  }

  #handleDocumentTagGroups(
    documentProjectTagGroup: DocumentProjectTagGroupModel[]
  ): void {
    const aiTagGroup = documentProjectTagGroup?.filter((tag) =>
      this.aiTagGroup.includes(tag.name)
    )
    const isAITagGroupExists = aiTagGroup?.[0] ? true : false
    this.reviewPanelViewState.setIsAITagExists(isAITagGroupExists)
    if (!isAITagGroupExists) {
      this.#resetTagGroupName()
    }
  }

  private fetchDocumentProjectTagGroup(): void {
    const documentProjectTagPayload: DocumentProjectTagRequestModel = {
      fileIds: this.selectedDocuments,
      projectId: this.projectId,
    }
    this.documentTagFacade.fetchDocumentProjectTagGroup(
      documentProjectTagPayload
    )
  }

  private fetchDocumentTags(): void {
    const documentTagPayload: DocumentTagPayloadModel = {
      selectedDocuments: this.selectedDocuments,
      searchResultTempTable: this.searchTempTable,
      projectId: this.projectId,
      isBatchSelected:
        this.tagActionType === TagActionType.TagAllInclusiveEmails
          ? true
          : this.isBulkDocument
          ? this.isBatchSelected
          : false,
    }
    this.documentTagFacade.fetchDocumentTags(documentTagPayload)
  }

  public selectDocumentTags(): void {
    this.documentTagFacade.selectDocumentTags$
      .pipe(
        filter((res) => !!res),
        distinctUntilChanged(),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((docTags: any) => {
        this.clonedDocumentTags = cloneDeep(docTags) // this is the cloned document tags
        this.initialClonedDocumentTags = cloneDeep(this.clonedDocumentTags) // this is the initial cloned document tags to identify the changes occured or not
        this.documentTags = docTags // this is the original document tags
        this.storeIsDocumentTagLoading(false)
        this.loadTagTreeWrapperComponent()
        this.changeDetectorRef.markForCheck()
      })
  }

  /**
   * Handles scenarios where the review panel is in a pop-out state and updates the
   * document tags in the pop-out review panel window when bulk tagging occurs.
   */

  #selectIsDocumentTagUpdated(): void {
    this.documentTagFacade.selectIsDocumentTagUpdated$
      .pipe(
        filter((isDocumentTagUpdated) => Boolean(isDocumentTagUpdated)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.reset()
        this.fetchProjectTags()
        this.#resetDocumentTagUpdateState()
      })
  }

  #selectTagAddOrUpdateResponses(): void {
    this.tagsFacade.selectTagAddOrUpdateSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((success) => {
        if (!success) return
        this.reset()
        this.newTagId = success.data
        this.fetchProjectTags()
      })
  }

  #selectTagReOrderResponses(): void {
    this.tagsFacade.selectTagReorderSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((success) => {
        if (!success) return
        this.reset()
        this.fetchProjectTags()
      })
  }

  #selectReloadTagGroupData(): void {
    this.documentTagFacade.selectReloadTagGroup$
      .pipe(
        filter((success) => Boolean(success)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.reset()
        this.fetchProjectTags()
        this.#resetReloadTagGroupData()
      })
  }

  #selectShowTagRuleList(): void {
    this.documentTagFacade.selectShowTagRuleList$
      .pipe(
        filter((showTagRuleList) => showTagRuleList),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.#hideTagRuleViolationDialog()
      })
  }

  #selectApplyTagErrorResponses(): void {
    this.documentTagFacade.applyDocumentTagErrorResponse$
      .pipe(
        filter((error) => Boolean(error)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((error) => {
        if (error?.message === 'Tag rule violated') {
          this.#hideNotifications()
          this.#openTagRuleVoliationDialog(error.data)
        }
        this.changeDetectorRef.markForCheck()
      })
  }

  #openTagRuleVoliationDialog(tagRuleVoliationDetail: any): void {
    import('../tag-rule-info-content/tag-rule-info-content.component').then(
      (td) => {
        this.#showTagRuleViolationDetail(tagRuleVoliationDetail)

        this.#launchDialogContent(td.TagRuleInfoContentComponent)
      }
    )
  }

  #showTagRuleViolationDetail(tagRuleVoliationDetail): void {
    this.documentTagFacade.setTagRuleViolationList(tagRuleVoliationDetail)
  }

  #launchDialogContent(dialogComponent: Type<unknown>): void {
    this.notificationRef = this.notificationService.show({
      content: dialogComponent,
      appendTo: this.vcr,
      position: { horizontal: 'center', vertical: 'top' },
      animation: { type: 'fade', duration: 100 },
      closable: true,
      cssClass: 'v-rule-notification',
    })
  }

  #hideNotifications(): void {
    if (this.notificationRef) {
      this.notificationRef.hide()
    }
  }

  public fetchTagSettings(): void {
    this.documentTagFacade.fetchTagSettings(this.projectId)
  }

  public toggleExpandCollapseAll(actionType: DocumentTagTreeListToggle): void {
    this.isTagListExpanded = actionType === DocumentTagTreeListToggle.EXPAND_ALL
    if (this.isTagListExpanded) {
      this.expandedKeys = this.expandedClonedKeys
    } else {
      this.expandedKeys = []
    }
    this.#expandedTagIdsChanged(this.expandedKeys)
    this.loadTagTreeWrapperComponent()
    this.changeDetectorRef.markForCheck()
  }

  #checkIfNewTagAdded(): void {
    if (!this.newTagExpandedIds) return
    this.expandedKeys = uniq([...this.expandedKeys, ...this.newTagExpandedIds])
  }

  #expandTagTreeIfNewTagAdded(tags): void {
    if (!this.newTagId) return
    const tagTreeKeyId = this.#findTagById(tags, this.newTagId)
    const newTagIds = tagTreeKeyId?.split('_').map(Number)
    this.newTagExpandedIds = uniq([...this.expandedKeys, ...newTagIds])
    this.#expandedTagIdsChanged(this.newTagExpandedIds)
    this.isTagListExpanded = true
  }

  #findTagById(tagtree, targetId): string | null {
    for (const node of tagtree) {
      if (node.TagId === targetId) {
        return node?.TreeKeyId // Found the tag!
      } else if (node?.children?.length > 0) {
        // Recursively search children
        const result = this.#findTagById(node.children, targetId)
        if (result) {
          return result // Propagate the result up
        }
      }
    }
    return null // Tag not found
  }

  #prepareTagTreeData(): void {
    if (this.isBulkDocument) return
    const isUserAndSystemTagGroup = this.selectedTagGroup().includes(
      TagGroupActionType.USER_SYSTEM
    )

    this.treeNodes = this.initialClonedTreeNodes?.filter((node) =>
      isUserAndSystemTagGroup
        ? !this.aiTagGroup.includes(node.TagName)
        : node.TagGroupName === this.selectedTagGroup()
    )
  }

  private loadTagTreeWrapperComponent(): void {
    this.#prepareTagTreeData()
    this.tagTreeWrapperComponentItem = {
      component: this.tagTreeWrapperComponent,
      inputs: {
        projectId: this.projectId,
        treeNodes: this.treeNodes,
        documentTags: this.documentTags,
        currentDocumentTagsState: this.clonedDocumentTags,
        expandedKeys: this.expandedKeys,
        isBulkDocument: this.isBulkDocument,
        searchTag: this.searchTag,
        onExpandedTagIdsChange: (expandedTagIds: number[]): void => {
          this.#expandedTagIdsChanged(expandedTagIds)
        },
        treeNodeSelected: (treeNodeEvent: unknown): void => {
          this.onTagSelected(treeNodeEvent)
        },
        isConflictDialog: this.isConflictDialog,
      },
    }
    this.isComponentReady = true
    this.setExpandCollapseVisibility()
    this.changeDetectorRef.markForCheck()
  }

  #expandedTagIdsChanged(expandedTagIds: number[]): void {
    this.documentTagFacade.setExpandedTagIds(expandedTagIds)
  }

  public onTagSelected(event): void {
    if (event['isExclusive']) {
      // sets exclusive enabled tag of selected tag group to false
      for (const doc in this.clonedDocumentTags) {
        if (
          this.clonedDocumentTags[doc].tagState &&
          event['selectedTagChildrenId']?.length &&
          event['selectedTagChildrenId'].includes(
            this.clonedDocumentTags[doc].tagId
          )
        ) {
          this.clonedDocumentTags[doc].tagState = false
          this.clonedDocumentTags[doc].reviewTagTriState = false
        }
      }
    }

    if (
      this.clonedDocumentTags &&
      Object.prototype.hasOwnProperty.call(this.clonedDocumentTags, event.tagId)
    ) {
      this.clonedDocumentTags[event.tagId].tagState = this.clonedDocumentTags[
        event.tagId
      ].reviewTagTriState = event.isChecked
      this.clonedDocumentTags[event.tagId].comments = event.comments
    }

    if (!this.isBulkDocument)
      this.reviewPanelViewState.addCurrentDocumentTags(event)
    const isTagDataModified = this.#getIsTagDataModified()

    this.documentTagFacade.updateUserSelectedDocumentTags(
      cloneDeep(this.clonedDocumentTags),
      isTagDataModified
    )
    this.loadTagTreeWrapperComponent()
    this.changeDetectorRef.markForCheck()
  }

  #getIsTagDataModified(): boolean {
    const initialClonedDocumentTags = Object.values(
      this.initialClonedDocumentTags
    ).map(({ tagId, tagState }) => ({ tagId, tagState }))

    const clonedDocumentTags = Object.values(this.clonedDocumentTags).map(
      ({ tagId, tagState }) => ({ tagId, tagState })
    )

    const isTagDataModified = !isEqual(
      initialClonedDocumentTags,
      clonedDocumentTags
    )
    return isTagDataModified
  }

  public setExpandCollapseVisibility(): void {
    const isTagExists = Boolean(this.treeNodes?.[0])
    this.showExpandCollapse.set(isTagExists)
  }

  private storeIsDocumentTagLoading(isDocumentTagLoading: boolean): void {
    this.documentTagFacade.setIsDocumentTagLoading(isDocumentTagLoading)
    this.changeDetectorRef.markForCheck()
  }

  private reset(): void {
    this.selectedDocuments = []
    this.newTagId = undefined
  }

  public getDynamicClass(): string {
    return "before:t-content-['Select___Tags']"
  }

  public tagGroupActionItemClick(action: TagGroupInfo): void {
    this.reviewPanelViewState.setSelectedTagGroup(action)
    this.loadTagTreeWrapperComponent()
  }

  public applyCopyTag(): void {
    this.copyDocumentTags().map((copyTag) => {
      const clonedTag = this.clonedDocumentTags[copyTag.tagId]
      if (clonedTag) {
        clonedTag.tagState = copyTag.isChecked
        clonedTag.comments = copyTag.comments
        this.reviewPanelViewState.addCurrentDocumentTags(copyTag)
      }
    })
    const isTagDataModified = this.#getIsTagDataModified()
    this.documentTagFacade.updateUserSelectedDocumentTags(
      cloneDeep(this.clonedDocumentTags),
      isTagDataModified
    )
    this.loadTagTreeWrapperComponent()
  }

  #hideTagRuleViolationDialog(): void {
    if (this.notificationRef) {
      this.documentTagFacade.resetDocumentTagState(['tagRuleViolationList'])
      this.notificationRef.hide()
    }
  }

  #resetTagGroupName(): void {
    const tagGroupInfo: TagGroupInfo = {
      title: TagGroupActionTitle.USER_SYSTEM,
      actionType: TagGroupActionType.USER_SYSTEM,
    }
    this.reviewPanelViewState.setSelectedTagGroup(tagGroupInfo)
  }

  #resetDocumentTagRuleState(): void {
    this.documentTagFacade.resetDocumentTagState([
      'tagRuleViolationList',
      'filterTagRuleId',
      'documentProjectTagGroup',
    ])
    this.#clearDocumentTags()
  }

  #resetCopyTagSelection(): void {
    this.reviewPanelViewState.resetCopyTagSelection()
  }

  #clearDocumentTags(): void {
    this.clonedDocumentTags = []
    this.initialClonedDocumentTags = []
    this.documentTags = []
  }

  #resetDocumentTagUpdateState(): void {
    this.documentTagFacade.resetDocumentTagState(['isDocumentTagUpdated'])
  }

  #resetReloadTagGroupData(): void {
    this.documentTagFacade.resetDocumentTagState(['reloadTagGroup'])
  }

  public ngOnDestroy(): void {
    this.#resetDocumentTagRuleState()
    this.#resetCopyTagSelection()
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
