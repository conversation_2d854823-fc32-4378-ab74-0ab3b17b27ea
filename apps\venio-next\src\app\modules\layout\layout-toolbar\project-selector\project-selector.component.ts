import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  Inject,
  inject,
  Injector,
  OnDestroy,
  OnInit,
  Optional,
  signal,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  Project,
  CaseInfoFacade,
  SearchFacade,
  CompositeLayoutState,
} from '@venio/data-access/review'
import { sortBy } from 'lodash'
import { Subject, ReplaySubject, debounceTime, takeUntil } from 'rxjs'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  IframeMessengerService,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { UserModel } from '@venio/shared/models/interfaces'
import {
  DocumentViewFacade,
  ModuleLoginStateService,
  ProjectFacade,
  UserFacade,
} from '@venio/data-access/common'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'
import { toSignal } from '@angular/core/rxjs-interop'
import { DialogService } from '@progress/kendo-angular-dialog'
import { FormsModule } from '@angular/forms'

@Component({
  selector: 'venio-project-selector',
  standalone: true,
  imports: [
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    FormsModule,
  ],
  templateUrl: './project-selector.component.html',
  styleUrl: './project-selector.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProjectSelectorComponent implements OnInit, OnDestroy {
  public readonly toDestroy$ = new Subject<void>()

  private breadcrumbFacade = inject(BreadcrumbFacade)

  private documentViewFacade = inject(DocumentViewFacade)

  private iframeMessengerFacade = inject(IframeMessengerFacade)

  private projectFacade = inject(ProjectFacade)

  private moduleLoginState = inject(ModuleLoginStateService)

  private readonly injector = inject(Injector)

  private readonly dialogService = inject(DialogService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private get projectId(): number {
    return +this.route.snapshot.queryParams['projectId']
  }

  private get isExternalUser(): boolean {
    return this.currentUser()?.userRole === 'external'
  }

  // list of all projects
  public projects: Project[]

  private currentUser = signal<Partial<UserModel>>({})

  /** list of projects filtered by search keyword */
  public filteredProjects: ReplaySubject<Project[]> = new ReplaySubject<
    Project[]
  >(1)

  // currently selected project id bound to the dropdown
  public readonly selectedProjectId = signal<number>(undefined)

  public currentProjectId: number

  // text to display when no project is available or matches search term
  public noDataText = 'No cases available'

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  private readonly unIndexMediaStatus = toSignal(
    this.projectFacade.selectunIndexMediaSuccess$
  )

  /** Computed property for the media processed status */
  public readonly unIndexMediaList = computed(() => {
    return this.unIndexMediaStatus()?.data
  })

  constructor(
    private caseFacade: CaseInfoFacade,
    private searchFacade: SearchFacade,
    private userFacade: UserFacade,
    public router: Router,
    private route: ActivatedRoute,
    private iframeMessengerService: IframeMessengerService,
    @Optional() @Inject('selectorView') public selectorView = 'home'
  ) {}

  public ngOnInit(): void {
    this.#fetchSearchDuplicationOption()
    this.#selectCurrentUser()
    this.#fetchCaseList()
    this.#loadCaseList()

    // fetch the case info for the initially loaded project
    this.#fetchCaseInfoById(this.projectId)
    this.#selectUnIndexMediaStatus()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #selectCurrentUser(): void {
    this.userFacade.selectCurrentUserSuccessResponse$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((success) => {
        this.currentUser.set(success?.data || {})
      })
  }

  #resetBreadcrumbState(): void {
    this.breadcrumbFacade.resetBreadcrumbCurrentStates()
  }

  /**
   * Fetches the list of projects from API, sets the initial filtered project list and selects the project based on query string param
   * @returns {void}
   */
  #loadCaseList(): void {
    this.caseFacade.getProjectList$
      .pipe(debounceTime(100), takeUntil(this.toDestroy$))
      .subscribe({
        next: (projects) => {
          //exclude service type cases from appearing in the case selector
          this.projects = projects.filter((p) => !p.isServiceTypeCase)
          // load the initial project list
          this.#setInitialFilteredCaseList()

          // if project id is provided in query string param, set the selected project id
          this.#setSelectedProjectId()
        },
      })
  }

  /**
   * Sets the initial filtered project list
   * @returns {void}
   */
  #setInitialFilteredCaseList(): void {
    this.filteredProjects.next(this.projects.slice())
  }

  /**
   * Sets the selected project id based on query string param
   * @returns {void}
   */
  #setSelectedProjectId(): void {
    if (+this.projectId > 0) {
      // we're setting selected project Id from query string param if supplied.
      this.selectedProjectId.set(+this.projectId)
      this.currentProjectId = +this.projectId
    }
  }

  /**
   * Fetches the list of projects from the API
   * @returns {void}
   */
  #fetchCaseList(): void {
    if (this.isExternalUser) {
      this.caseFacade.fetchExternalUserProjectList(this.projectId)
    } else {
      this.caseFacade.fetchProjectList()
    }
  }

  /**
   * Fetches the case info for the selected project
   * @param {number} projectId the selected project id
   * @returns {void}
   */
  #fetchCaseInfoById(projectId: number): void {
    this.caseFacade.fetchCaseInfoById(projectId)
  }

  /**
   * Reset project-dependent states and reload the data after route change
   * @returns {void}
   */
  #clearStateAndLoadAfterRouteChange(): void {
    this.layoutState.userLayouts.set([])
    this.layoutState.userSelectedLayout.set(null)
    this.documentViewFacade.resetDocumentViewListState()
    this.searchFacade.clearSearchResponse()
    this.searchFacade.resetSearchInputControls()
    this.#resetBreadcrumbState()
    this.documentViewFacade.fetchDocumentViewList()
    this.iframeMessengerFacade.resetMessengerState(MessageType.SEARCH_CHANGE)
  }

  #fetchSearchDuplicationOption(): void {
    this.projectFacade.fetchSearchOptions(this.projectId)
  }

  /**
   * Filters project list based on input provided in the search box
   * @param {string} filterTerm search term entered the project selection text input
   * @returns {void}
   */
  public onFilterChange(filterTerm: string): void {
    // set the "no data" text based on the filter term
    if (filterTerm) this.noDataText = 'No matching case found'
    else this.noDataText = 'No cases available'

    this.filterProjects(filterTerm, this.projects)
  }

  /** filters the project based on the character in search text
   * @param {string} search - the search keyword
   * @param {Project[]} projects - the list of projects
   * @returns {void}
   */
  private filterProjects = (search: string, projects: Project[]): void => {
    if (!projects) {
      return
    }
    projects = sortBy(projects, (c) => c.projectName)
    // get the search keyword
    if (!search) {
      this.filteredProjects.next(projects.slice())
      return
    }
    search = search.toLowerCase()

    // filter the projects
    this.filteredProjects.next(
      projects.filter((c) => c.projectName.toLowerCase().indexOf(search) > -1)
    )
  }

  /**
   * Handles project change event
   * @param {number} projectId newly selected project id
   * @returns {void}
   */
  public onProjectChange(projectId: number): void {
    this.selectedProjectId.set(projectId)
    this.fetchUnIndexMediaStatus(projectId)
  }

  public fetchUnIndexMediaStatus(projectId: number): void {
    this.projectFacade.fetchUnIndexMediaStatus(projectId)
  }

  #selectUnIndexMediaStatus(): void {
    effect(
      () => {
        const unIndexMediaList = this.unIndexMediaList()
        if (!unIndexMediaList) return
        this.#checkUnIndexMediaStatus()
      },
      { injector: this.injector, allowSignalWrites: true }
    )
  }

  #checkUnIndexMediaStatus(): void {
    if (this.unIndexMediaList()?.[0]) {
      this.#openUnIndexMediaStatusDialog()
    } else {
      this.#handleProjectChange(this.selectedProjectId())
    }
  }

  #openUnIndexMediaStatusDialog(): void {
    import(
      '../../../launchpad/media-processing-status-dialog/media-processing-status-dialog.component'
    ).then((td) => {
      const dialogRef = this.dialogService.open({
        content: td.MediaProcessingStatusDialogComponent,
        appendTo: this.viewContainerRef,
        maxWidth: '900px',
        maxHeight: '650px',
        width: '80%',
        height: '90vh',
      })

      dialogRef.result.subscribe((result: any) => {
        if (result.shouldRedirect) {
          this.#handleProjectChange(this.selectedProjectId())
        } else {
          this.selectedProjectId.set(this.currentProjectId)
        }
      })
    })
  }

  #handleProjectChange(projectId: number): void {
    this.#resetUnIndexMediaStatus()

    this.currentProjectId = projectId

    this.caseFacade.isProjectManuallyChanged.set(true)

    // show loading animation
    this.searchFacade.IsSearchLoading(true)

    // navigate to another project's review page
    this.navigateToProject(projectId)

    // fetch the case info for the selected project
    this.#fetchCaseInfoById(projectId)

    this.moduleLoginState.updateProjectId(projectId)

    // send project change message to old vod to update the url
    this.notifyParentWindow(projectId)
  }

  #resetUnIndexMediaStatus(): void {
    this.projectFacade.resetProjectState([
      'isMediaStatusLoading',
      'unIndexMediaSuccessResponse',
      'unIndexMediaErrorResponse',
    ])
  }

  /** Navigates to the documents route with the updated projectId query parameter
   * @param {number} projectId - the project id
   * @returns {void}
   */
  private navigateToProject(projectId: number): void {
    // Grab the existing query parameters
    const queryParams = { ...this.route.snapshot.queryParams }

    // Update the projectId query parameter
    queryParams['projectId'] = projectId.toString()

    // Construct navigation extras
    const navigationExtras: NavigationExtras = {
      queryParams, // Include the updated query parameters
      onSameUrlNavigation: 'reload', // Ensure route reloads if navigation is to the same URL
      replaceUrl: true,
      skipLocationChange: false,
    }

    // Navigate to the same route with updated query parameters
    this.router.navigate(['/documents'], navigationExtras).then(() => {
      // We need to ensure the navigation happens before we clear the state
      // so that the new project's data is loaded correctly by updated project id
      this.#clearStateAndLoadAfterRouteChange()

      this.#fetchSearchDuplicationOption()
    })
  }

  /** Notifies parent window(vod2) about the project change in order to update the url
   * @param {number} projectId - the project id
   * @returns {void}
   */
  private notifyParentWindow(projectId: number): void {
    this.iframeMessengerService.sendMessage({
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content: {
          isProjectIdChanged: true,
          projectId: projectId,
        },
      },
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
    })
  }
}
