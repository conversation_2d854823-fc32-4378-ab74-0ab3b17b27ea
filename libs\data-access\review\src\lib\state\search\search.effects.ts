import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import {
  catchError,
  concatMap,
  filter,
  map,
  mergeMap,
  of,
  switchMap,
  withLatestFrom,
} from 'rxjs'
import {
  EmailThreadVisibleType,
  IndexedDBViewerType,
  ReviewDataSourceType,
  ReviewViewType,
} from '../../models/constants/search.enums'
import { ModuleLoginService } from '../../services/module-login.service'
import { SearchService } from '../../services/search.service'
import * as fromSearchActions from './search.actions'
import * as fromStarupActions from '../startups/startups.actions'
import * as fromBatchReviewActions from '../batch-review/batch-review.actions'
import * as fromSearchResultActions from '../search-result/search-result.actions'
import { SearchFacade } from './search.facade'
import {
  BreadCrumb,
  NavigationType,
  SearchDupOption,
  SearchRequestModel,
  SearchResponseModel,
  SortingFieldModel,
} from '../../models/interfaces/search.model'
import { ActivatedRoute } from '@angular/router'
import { BatchReviewFacade } from '../batch-review/batch-review.facade'
import { ReviewParamService } from '../../services/review-param.service'
import { DynamicFolderModel, FolderModel } from '../../models/interfaces'
import { Store } from '@ngrx/store'
import { IndexedDBHandlerService, ReviewSetStateService } from '../../services'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { HttpErrorResponse } from '@angular/common/http'
import { ViewFacade } from '../view'
import { SortType, ViewModel } from '../../models/interfaces/view.model'
import { ReviewFacade } from '../review'
import { resetDocumentState } from '../documents'
import { extractTagRuleIdPatterns } from '@venio/util/utilities'

@Injectable()
export class SearchEffects {
  public get _documentShareToken(): string {
    return this.route.snapshot.queryParams['docShareToken']
  }
  private reviewSetState = inject(ReviewSetStateService)
  constructor(
    private store: Store,
    private readonly actions$: Actions,
    private searchFacade: SearchFacade,
    private batchReviewFacade: BatchReviewFacade,
    private searchService: SearchService,
    private moduleLoginService: ModuleLoginService,
    private route: ActivatedRoute,
    private reviewParamService: ReviewParamService,
    private indexDbService: IndexedDBHandlerService,
    private viewFacade: ViewFacade,
    private reviewFacade: ReviewFacade
  ) {}

  /**
   * Calls the search service
   * Invokes postSearch action for getting search request response (The response is not search results)
   */
  public search$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.search),
      concatMap((action) => {
        this.store.dispatch(
          fromSearchActions.IsSearchLoadingAction({ isSearchLoading: true })
        )
        return of(action).pipe(
          withLatestFrom(
            this.searchFacade.getSearchResponse$,
            //this.batchReviewFacade.getSelectedReviewSetBatchInfo$,
            this.searchFacade.getBreadcrumbs$,
            this.reviewParamService.projectId,
            this.viewFacade.selectUserDefaultView$,
            this.reviewFacade.getReviewViewType$,
            this.searchFacade.getDynamicFolderSearchScope$,
            this.searchFacade.getStaticFolderSearchScope$,
            this.reviewFacade.getVisibleEmailType$,
            this.searchFacade.getIncludePC$,
            this.searchFacade.getSearchDupOption$
          )
        )
      }),
      switchMap(
        ([
          action,
          searchResponse,
          //selectedBatchInfo,
          stateBreadcrumbs,
          projectId,
          selectedView,
          reviewViewType,
          dynamicFolderScopeFromStore,
          staticFolderScopeFromStore,
          visibleEmailType,
          includeFamily,
          searchDupOption,
        ]: [
          any,
          SearchResponseModel,
          //BatchModel,
          BreadCrumb[],
          number,
          ViewModel,
          ReviewViewType,
          DynamicFolderModel,
          FolderModel,
          EmailThreadVisibleType,
          boolean,
          SearchDupOption
        ]) => {
          const media = null
          const selectedMedias = null

          const tempTables = searchResponse?.tempTables
          const inputs = action.payload
          let navigationType = NavigationType.Media
          if (
            inputs?.navigationType !== null &&
            inputs?.navigationType !== undefined
          )
            navigationType = inputs.navigationType
          else if (inputs.folderList?.length || staticFolderScopeFromStore)
            navigationType = NavigationType.Folder
          else if (inputs.dynamicFolderScope || dynamicFolderScopeFromStore)
            navigationType = NavigationType.DynamicFolder
          else navigationType = NavigationType.Media

          const searchRequest: SearchRequestModel = {
            includePC:
              inputs?.includePC !== null && inputs?.includePC !== undefined
                ? inputs.includePC
                : includeFamily ?? false,
            searchExpression: 'extension="xls"', //inputs?.searchExpression ?? 'fileid>0',
            isForwardFilter: inputs.isForwardFilter,
            projectId: projectId.toString(),
            lstMedia: inputs?.medialist?.length
              ? inputs?.medialist?.map((id) => String(id)) //apply media list if sent from inputs.
              : selectedMedias?.length
              ? selectedMedias.map((id) => String(id)) // apply selected media ids.
              : media?.isAllMediaProcessed
              ? media?.mediaIds?.map((id) => `${id}`) // apply all processed media ids
              : media?.processedMediaIds.map((id) => `${id}`), // apply processed media IDs only
            lstFolder: inputs?.folderList?.length
              ? inputs?.folderList?.map((id) => id)
              : staticFolderScopeFromStore &&
                navigationType === NavigationType.Folder
              ? [staticFolderScopeFromStore?.folderId]
              : null,
            dynamicFolderScope: inputs?.dynamicFolderScope
              ? inputs.dynamicFolderScope
              : dynamicFolderScopeFromStore &&
                navigationType === NavigationType.DynamicFolder
              ? `folderId:${dynamicFolderScopeFromStore?.folderId}|isGlobal:${dynamicFolderScopeFromStore?.isGlobal}`
              : null,
            userType:
              localStorage.getItem('DocShareUserRole')?.toLowerCase() ===
              'external'
                ? 'EXTERNAL'
                : 'INTERNAL',
            searchDuplicateOption:
              inputs?.searchDuplicateOption !== null &&
              inputs?.searchDuplicateOption !== undefined
                ? inputs?.searchDuplicateOption
                : searchDupOption ?? SearchDupOption?.DEFAULT,
            searchGuid: tempTables ? tempTables.searchGuid : null,
            reviewSetId: inputs.reviewSetId, // ?? reviewSetId,
            viewType: reviewViewType ?? ReviewViewType.Search,
            baseGUID: inputs?.isResetBaseGuid
              ? ''
              : tempTables
              ? tempTables.baseGUID
              : '',
            batchId: this.reviewSetState.batchId(),
            isSavedSearch: inputs?.isSavedSearch ?? false,
            isLoadFile: inputs?.isLoadFile ?? false,
            isSearchFromHistory: inputs?.isSearchFromHistory ?? false,
            searchId: inputs?.searchHistoryId,
            dynamicFolderId: inputs?.dynamicFolderId,
            isDynamicFolderGlobal: inputs?.isDynamicFolderGlobal ?? false,
            documentShareToken: this._documentShareToken,
            isSqlMode: inputs?.isSqlMode ?? false,
            searchQuery: inputs?.searchQuery,
            navigationType: navigationType,
            isFilterSearch: inputs?.isFilterSearch ?? false,
            sortFieldModel: selectedView?.viewSortSettings?.map((f) => {
              return {
                field: f.fieldName,
                order: f.fieldSortType === SortType.Ascending ? 'ASC' : 'DESC',
                keepFamilyTogether: true,
              } as SortingFieldModel
            }),
            showOnlyInclusiveEmailsThreads:
              visibleEmailType === EmailThreadVisibleType.InclusiveEmailOnly,
            viewTagRuleConflictFiles: inputs?.viewTagRuleConflictFiles,
          }

          return this.searchService.search$(searchRequest).pipe(
            switchMap((response: any) => {
              this.indexDbService.clearDB(IndexedDBViewerType.Text)
              const searchResponse = response?.data
              const actions = []

              const docCount =
                response?.data?.searchResultIntialParameters?.totalHitCount || 0

              // Extract tag rule id patterns from search expression
              // This is used to toggle the resolve conflict link if the syntax is tag rule id
              // and the search result is not empty
              const tagRuleIdPatternList =
                docCount > 0
                  ? extractTagRuleIdPatterns(
                      searchRequest.searchExpression.trim() || ''
                    )
                  : {
                      ruleIds: [],
                      syntax: [],
                    }
              const updatedSearchParams = {
                ...searchRequest,
                tempTableResponse: response?.data?.tempTables,
                docCount,
              }
              actions.push(
                fromStarupActions.updateSearchRequestParam({
                  searchParams: updatedSearchParams,
                })
              )
              if (tagRuleIdPatternList.ruleIds.length) {
                actions.push(
                  fromSearchActions.updateTagRuleIdPatternList({
                    tagRuleIdPatternList,
                  })
                )
              }
              if (searchResponse === undefined) {
                actions.push(fromSearchActions.clearSearchResponse())
                actions.push(
                  fromSearchResultActions.setSearchResults({
                    payload: { searchResults: [] },
                  })
                )
                actions.push(
                  fromSearchActions.IsSearchLoadingAction({
                    isSearchLoading: false,
                  })
                )
                this.searchFacade.clearSearchResult$.next()
              } else {
                if (searchResponse?.error?.errorStatus) {
                  //search error occurred
                } else {
                  if (this.reviewSetState.reviewSetId() > 0) {
                    actions.push(
                      fromSearchActions.setReviewDatasourceType({
                        reviewDataSourceType: ReviewDataSourceType.Review,
                      })
                    )
                    actions.push(
                      fromBatchReviewActions.fetchSelectedReviewBatchInfo({
                        projectId: projectId,
                        reviewSetId: this.reviewSetState.reviewSetId(),
                        batchId:
                          searchResponse?.searchResultIntialParameters?.batchId,
                      })
                    )
                    actions.push(
                      fromBatchReviewActions.setReviewBatchCheckoutResponse({
                        payload: { batchCheckoutResponse: searchResponse },
                      })
                    )
                    actions.push(
                      fromBatchReviewActions.addModuleLogin({
                        projectId: projectId,
                        moduleLoginInfo:
                          this.moduleLoginService.getModuleLoginModel(
                            this.reviewParamService.projectLoginId,
                            this.reviewSetState.reviewSetId()
                          ),
                      })
                    )
                  } else {
                    actions.push(
                      fromSearchActions.setReviewDatasourceType({
                        reviewDataSourceType: ReviewDataSourceType.Search,
                      })
                    )
                  }
                  actions.push(
                    fromSearchActions.searchSuccess({
                      payload: { searchResponse: searchResponse },
                    })
                  )
                  actions.push(fromSearchActions.postSearchReview())
                  actions.push(
                    resetDocumentState({
                      stateKey: [
                        'selectedDocuments',
                        'unselectedDocuments',
                        'isBatchSelected',
                      ],
                    })
                  )
                }
              }
              return actions
            }),
            catchError((err: unknown) => {
              const httpError = err as HttpErrorResponse
              return of(
                fromSearchActions.searchFailure({
                  searchFailureResponse: httpError.error,
                })
              )
            })
          )
        }
      )
    )
  )

  /**
   * Handles post search tasks such as setting temp tables details, search result parameters, and search error message in the store
   * Also sets the current document table page and resets documents selection to the first item of the page
   * i.e. deselects everything and selects only the first item of the page
   */

  public postSearchReview1$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.postSearchReview),
      concatMap((action) =>
        of(action).pipe(withLatestFrom(this.searchFacade.getSearchResponse$))
      ),
      filter(([action, searchResponse]) => !!searchResponse),
      switchMap(([action, searchResponse]) => {
        const actions = []
        if (searchResponse) {
          actions.push(
            fromSearchResultActions.storeDocumentTablePaging({
              payload: {
                pageNumber:
                  searchResponse.searchResultIntialParameters.currentPage,
              },
            }),
            fromSearchResultActions.fetchDocumentTablePage({
              payload: {
                resetSelectionItem: '', // setting to empty string to set none as selected documents
              },
            })
          )
        }
        // Dispatch all actions pushed to the actions array
        return actions
      })
    )
  )

  public fetchSearchHistory$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.fetchSearchHistory),
      mergeMap((action) =>
        this.searchService
          .fetchSearchHistoryLog$(
            action.projectId,
            action.searchHistoryRequestModel
          )
          .pipe(
            map((searchHistoryResponse: ResponseModel) => {
              return fromSearchActions.fetchSearchHistorySuccess({
                searchHistoryResponse,
                isSavedSearch:
                  action.searchHistoryRequestModel.getSavedSearchOnly,
              })
            }),
            catchError((message: unknown) =>
              of(fromSearchActions.fetchSearchHistoryFailure({ message }))
            )
          )
      )
    )
  )

  public deleteSearchHistory$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.deleteSearchHistory),
      mergeMap((action) =>
        this.searchService
          .deleteSearchHistory$(
            action.projectId,
            action.delSearchHistoryRequestModel
          )
          .pipe(
            map((delSearchHistorySuccessResponse: ResponseModel) => {
              return fromSearchActions.deleteSearchHistorySuccess({
                delSearchHistorySuccessResponse,
              })
            }),
            catchError((error: unknown) => {
              const httpError = error as HttpErrorResponse
              return of(
                fromSearchActions.deleteSearchHistoryFailure({
                  delSearchHistoryFailureResponse: httpError.error,
                })
              )
            })
          )
      )
    )
  )

  public fetchDSJobStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.fetchDSJobStatus),
      mergeMap((action) =>
        this.searchService.fetchDSJobStatus$(action.dsStatus).pipe(
          map((fetchDSJobStatusSuccessResponse: ResponseModel) => {
            return fromSearchActions.fetchDSJobStatusSuccess({
              fetchDSJobStatusSuccessResponse,
            })
          }),
          catchError((error: unknown) => {
            const httpError = error as HttpErrorResponse
            return of(
              fromSearchActions.fetchDSJobStatusFailure({
                fetchDSJobStatusFailureResponse: httpError.error,
              })
            )
          })
        )
      )
    )
  )

  public fetchDSJobStatusCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.fetchDSJobStatusCount),
      mergeMap((action) =>
        this.searchService.fetchDSJobStatusCount$(action.dsStatus).pipe(
          map((fetchDSJobStatusCountSuccessResponse: ResponseModel) => {
            return fromSearchActions.fetchDSJobStatusCountSuccess({
              fetchDSJobStatusCountSuccessResponse,
            })
          }),
          catchError((error: unknown) => {
            const httpError = error as HttpErrorResponse
            return of(
              fromSearchActions.fetchDSJobStatusCountFailure({
                fetchDSJobStatusCountFailureResponse: httpError.error,
              })
            )
          })
        )
      )
    )
  )

  public fetchSavedSearchWithTags$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.fetchSavedSearchWithTags),
      mergeMap((action) =>
        this.searchService.getSavedSearchWithTags$(action.projectId).pipe(
          map((fetchSavedSearchWithTagsSuccessResponse: ResponseModel) => {
            return fromSearchActions.fetchSavedSearchWithTagsSuccess({
              fetchSavedSearchWithTagsSuccessResponse,
            })
          }),
          catchError((error: unknown) => {
            const httpError = error as HttpErrorResponse
            return of(
              fromSearchActions.fetchSavedSearchWithTagsFailure({
                fetchSavedSearchWithTagsFailureResponse: httpError.error,
              })
            )
          })
        )
      )
    )
  )

  public saveSearch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(fromSearchActions.saveSearch),
      mergeMap((action) =>
        this.searchService
          .saveSearch$(action.projectId, action.saveSearchRequest)
          .pipe(
            map((response: any) => {
              if (typeof response === 'string') {
                const saveSearchFailureResponse: ResponseModel = {
                  data: '',
                  message: response,
                  status: 'warning',
                }
                return fromSearchActions.saveSearchFailure({
                  saveSearchFailureResponse,
                })
              } else if (response < 0) {
                const saveSearchFailureResponse: ResponseModel = {
                  data: response,
                  message: 'Search Save Failed',
                  status: 'error',
                }
                return fromSearchActions.saveSearchFailure({
                  saveSearchFailureResponse,
                })
              }
              const saveSearchSuccessResponse: ResponseModel = {
                data: response,
                message: 'Search Saved Successfully',
                status: 'success',
              }
              return fromSearchActions.saveSearchSuccess({
                saveSearchSuccessResponse,
              })
            }),
            catchError((error: unknown) => {
              const httpError = error as HttpErrorResponse
              return of(
                fromSearchActions.saveSearchFailure({
                  saveSearchFailureResponse: httpError.error,
                })
              )
            })
          )
      )
    )
  )
}
