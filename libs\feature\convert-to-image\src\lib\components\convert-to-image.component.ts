import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core'
import {
  BulkImageRequestModel,
  ConvertDocumentFacade,
  ConvertDocumentTab,
  ConvertToImageFacade,
  ImageFileTypeDetailsResponseModel,
  ImageSummaryResponseModel,
  ResponseMessage,
  SelectedImageFileTypeDetailsRequestModel,
  SystemBatesModel,
} from '@venio/data-access/review'
import { Subject, combineLatest, debounceTime, filter, takeUntil } from 'rxjs'
import { cloneDeep } from 'lodash'
import { ConverToImageFormService } from '../services/convert-to-image-form.service'
import { FormGroup } from '@angular/forms'
import { DetailGridColumn, JobStatusFacade } from '@venio/data-access/common'

@Component({
  selector: 'venio-convert-to-image',
  templateUrl: './convert-to-image.component.html',
  styleUrls: ['./convert-to-image.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConvertToImageComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private resizeEvent = new Subject<Event>()

  public summaryData: ImageSummaryResponseModel[] = []

  public fileTypeData: ImageFileTypeDetailsResponseModel[] = []

  public fileTypeDataUpdated: ImageFileTypeDetailsResponseModel[] = []

  public updatedSelection: ImageFileTypeDetailsResponseModel[] = []

  public showSpinner: boolean

  public advancedOptionExpansionState = false

  public detailsPanelExpansionState = true

  public pageLimitdb = [
    'Max Pages',
    'All Pages',
    '10',
    '100',
    '200',
    '300',
    '400',
    '500',
    '1000',
    '2000',
  ]

  public updateFileTypesOnly = false

  public fileTypeGridHeight: number

  @ViewChild('treelistContainer', { static: true })
  private treelistContainerDivRef: ElementRef

  public get imageForm(): FormGroup {
    return this.convertToImageFormService.imageForm
  }

  public printImageSourceComponent = import(
    './advanced-option/advanced-option.component'
  ).then(({ AdvancedOptionComponent }) => AdvancedOptionComponent)

  public jobStatusComponent = import('@venio/feature/job-status').then(
    (m) => m.JobStatusComponent
  )

  constructor(
    private readonly convertToImageFacade: ConvertToImageFacade,
    private readonly convertDocumentFacade: ConvertDocumentFacade,
    private readonly convertToImageFormService: ConverToImageFormService,
    private readonly jobStatusFacade: JobStatusFacade,
    private readonly cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.showSpinner = true
    this.initSlices()

    // Listening for resize event to set grid size
    this.resizeEvent
      .pipe(debounceTime(200), takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.resizeFileTypeGrid()
        this.cdr.markForCheck()
      })

    const columns: DetailGridColumn[] = [
      { field: 'JobId', title: 'Job Id', width: 60 },
      { field: 'DocumentCount', title: 'No. of Documents', width: 130 },
      { field: 'UserName', title: 'Requested By', width: 160 },
      { field: 'StartTime', title: 'Start Time', width: 130 },
      { field: 'EndTime', title: 'End Time', width: 130 },
      { field: 'TotalTime', title: 'Total Time', width: 100 },
      { field: 'Status', title: 'Status', width: 70 },
    ]
    this.jobStatusFacade.setColumnNames(columns)
  }

  private initSlices(): void {
    this.initLaunchOptionChangeEvents()
    this.initImageSummaryLoad()
    this.initImageFileTypeLoad()
    this.initConvertButton()
  }

  private initConvertButton(): void {
    this.convertDocumentFacade.startConvertDocument$
      .pipe(
        filter((tab) => !!tab && tab === ConvertDocumentTab.ImageTab),
        takeUntil(this.toDestroy$)
      )
      .subscribe(() => {
        this.startConvert()
      })
  }

  public ngOnDestroy(): void {
    this.convertDocumentFacade.resetStartConvertDocument()
    this.convertToImageFacade.resetConvertToImageState()
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Triggers the event handler subject to set tree list size
   * @param {Event} event Event object
   * @returns {void}
   */
  @HostListener('window:resize', ['$event'])
  public onContainerResize(event: Event): void {
    this.resizeEvent.next(event)
  }

  /**
   * Sets the tree list height to the container height minus 8px
   * @returns {void}
   */
  private resizeFileTypeGrid(): void {
    // set initial height to 0
    this.fileTypeGridHeight = 0
    // set timeout to wait for the container to finish resizing
    setTimeout(() => {
      const divElement = this.treelistContainerDivRef.nativeElement
      if (divElement.offsetHeight - 8 < 140) this.fileTypeGridHeight = 140
      else this.fileTypeGridHeight = divElement.offsetHeight - 8
      this.cdr.markForCheck()
    }, 30)
  }

  private initLaunchOptionChangeEvents(): void {
    combineLatest([
      this.convertDocumentFacade.getLaunchOptionState$,
      this.convertDocumentFacade.getRelaunchOptionState$,
    ])
      .pipe(debounceTime(50), takeUntil(this.toDestroy$))
      .subscribe(([launch, relaunch]) => {
        this.showSpinner = true
        this.convertToImageFacade.fetchImageSummaryFileTypes(
          launch,
          relaunch,
          this.updateFileTypesOnly
        )
        this.updateFileTypesOnly = true
        this.cdr.markForCheck()
      })
  }

  private initImageSummaryLoad(): void {
    this.convertToImageFacade.getImageSummary$
      .pipe(
        filter((s) => !!s),
        debounceTime(100),
        takeUntil(this.toDestroy$)
      )
      .subscribe((summary: ImageSummaryResponseModel[]) => {
        if (summary !== null) {
          this.summaryData = summary
          const previousQueueCount =
            summary.find(
              (x) => x.titleKey === 'Previously Added to Image Queue'
            ).countValue ?? 0
          const remainingQueueCount =
            summary.find((x) => x.titleKey === 'Remaining to be Queued')
              .countValue ?? 0

          const enableRelaunch = previousQueueCount > 0
          const enableLaunch = remainingQueueCount > 0
          this.convertDocumentFacade.setLaunchOption(enableLaunch, false)
          this.convertDocumentFacade.setLaunchOptionAvailability(enableLaunch)
          this.convertDocumentFacade.setRelaunchOptionAvailability(
            enableRelaunch
          )
        }
        this.cdr.markForCheck()
      })
  }

  private initImageFileTypeLoad(): void {
    this.convertToImageFacade.getImageFileTypes$
      .pipe(debounceTime(500), takeUntil(this.toDestroy$))
      .subscribe((fileType: ImageFileTypeDetailsResponseModel[]) => {
        this.showSpinner = false
        this.cdr.markForCheck()
        this.resizeFileTypeGrid()

        if (fileType === undefined || fileType === null) {
          this.fileTypeData = []
          return
        }

        const _fileType = JSON.parse(JSON.stringify(fileType))
        if (_fileType !== null) {
          if (_fileType.length > 0) {
            _fileType.map(
              (filetypeModel: ImageFileTypeDetailsResponseModel) => {
                filetypeModel.selected = true
                filetypeModel.pageLimitdb = this.pageLimitdb

                if (
                  [22, 15, 20, 24, 26, 14].includes(
                    filetypeModel.fileTypeGroupId
                  ) // ppt, excel, word, pdf, outlook
                ) {
                  filetypeModel.engineDb = [
                    ...getImagingEngine(),
                    this.getImagingEngineName(filetypeModel.fileTypeGroupId),
                  ]
                } else {
                  filetypeModel.engineDb = getImagingEngine()
                }

                if (
                  filetypeModel.fileTypeGroupId === 9 ||
                  filetypeModel.fileTypeGroupId === 29 //xml and text
                ) {
                  filetypeModel.colorDb = getColorOption()
                } else {
                  filetypeModel.colorDb = [
                    ...getColorOption(),
                    'COLOR',
                    'COLOR_FOR_COLOR',
                  ]
                }
              }
            )
          }
          this.fileTypeData = _fileType
          this.fileTypeDataUpdated = cloneDeep(this.fileTypeData)
        }
      })

    function getImagingEngine(): string[] {
      return [
        'Default',
        'Non-Native Imaging Engine',
        'Generic Imaging Engine',
        'Do not Convert',
      ]
    }

    function getColorOption(): string[] {
      return ['Default', 'BLACK_AND_WHITE', 'GRAYSCALE']
    }
  }

  private getImagingEngineName(fileTypeGroupId: number): string {
    switch (fileTypeGroupId) {
      case 22:
      case 15:
        return 'MS PowerPoint Imaging Engine'
      case 20:
        return 'MS Excel Imaging Engine'
      case 24:
        return 'MS Word Imaging Engine'
      case 26:
        return 'Adobe PDF Imaging Engine'
      case 14:
        return 'MS Outlook Imaging Engine'
      default:
        return ''
    }
  }

  public startConvert(): void {
    const selectedItems = this.updatedSelection.filter(
      (item) => item.fileTypeGroupId > 0 && item.selected
    )

    const selectedFileTypes = selectedItems
      .map((fileType) => fileType?.fileTypeGroupId)
      .filter((fileType) => !!fileType)

    const selectedImageFileTypeList: SelectedImageFileTypeDetailsRequestModel[] =
      this.fileTypeDataUpdated
        .filter(
          (data) =>
            data.fileTypeGroupId > 0 &&
            selectedFileTypes.includes(data.fileTypeGroupId)
        )
        .map((item) => {
          return {
            fileTypeGroupId: item.fileTypeGroupId,
            myPageLimit: item.myPageLimit,
            cnt: item.cNT,
            imagingEngine: item.imagingEngine,
            colorConversion: item.colorConversion,
          }
        })

    const imageSettings = Object.assign(
      {},
      <
        {
          systemBates: SystemBatesModel
          pageLimitOption: string
          customMaxpageLimit: number
          imageDimensionSettings: {
            imageDimension: string
            customWidth: number
            customHeight: number
            dpi: number
          }
        }
      >this.imageForm.value
    )

    const bulkImageRequestModel: Partial<BulkImageRequestModel> = {
      selectedImageFileTypeList: selectedImageFileTypeList,
      systemBates: imageSettings.systemBates,
      customMaxpageLimit:
        imageSettings.pageLimitOption === 'CUSTOM_PAGE_LIMIT'
          ? imageSettings.customMaxpageLimit
          : -1,
      outputImageDimension: {
        originalImageDimension:
          imageSettings.imageDimensionSettings.imageDimension === 'ORIGINAL',
        customDimension:
          imageSettings.imageDimensionSettings.imageDimension === 'CUSTOM',
        defaultDimension:
          imageSettings.imageDimensionSettings.imageDimension === 'DEFAULT',
        height:
          imageSettings.imageDimensionSettings.imageDimension === 'CUSTOM'
            ? imageSettings.imageDimensionSettings.customHeight
            : -1,
        width:
          imageSettings.imageDimensionSettings.imageDimension === 'CUSTOM'
            ? imageSettings.imageDimensionSettings.customWidth
            : -1,
        dpi: imageSettings.imageDimensionSettings.dpi,
      },
    }

    // validation
    const responseMessage: ResponseMessage = this.validateImagingSettings(
      selectedImageFileTypeList,
      imageSettings
    )
    if (responseMessage !== null) {
      this.convertDocumentFacade.setResponseMessage(responseMessage)
      return
    }

    this.convertToImageFacade.queueForImageConversion(bulkImageRequestModel)

    const queueResponse =
      this.convertToImageFacade.getImageConversionQueueResponse$
        .pipe(
          filter((response: ResponseMessage) => !!response),
          takeUntil(this.toDestroy$)
        )
        .subscribe((response: ResponseMessage) => {
          this.convertToImageFacade.clearImageConversionQueueResponse()
          this.convertToImageFormService.resetForm()
          if (this.advancedOptionExpansionState) {
            this.advancedOptionExpansionState = false
          }
          if (this.detailsPanelExpansionState) {
            this.detailsPanelExpansionState = false
          }
          this.cdr.markForCheck()
          queueResponse.unsubscribe()
        })
  }

  public validateImagingSettings(
    selectedImageFileTypeList: SelectedImageFileTypeDetailsRequestModel[],
    imageSettings: {
      systemBates: SystemBatesModel
      pageLimitOption: string
      customMaxpageLimit: number
      imageDimensionSettings: {
        imageDimension: string
        customWidth: number
        customHeight: number
        dpi: number
      }
    }
  ): ResponseMessage {
    if (selectedImageFileTypeList.length === 0) {
      return {
        success: false,
        message: 'Please select at least one file type to send to image.',
      }
    }

    let showPageLimitWarning: boolean
    selectedImageFileTypeList.forEach((item) => {
      if (
        imageSettings.pageLimitOption === 'CUSTOM_PAGE_LIMIT' &&
        Number(item.myPageLimit) > imageSettings.customMaxpageLimit
      ) {
        showPageLimitWarning = true
      }
    })

    if (showPageLimitWarning) {
      return {
        success: false,
        message:
          'Page limit of file type can not be set more than the custom page limit set in advanced option',
      }
    }

    if (
      (imageSettings.pageLimitOption === 'CUSTOM_PAGE_LIMIT' &&
        imageSettings.customMaxpageLimit <= 0) ||
      imageSettings.systemBates.startingNum <= 0 ||
      imageSettings.systemBates.padding <= 0
    ) {
      return {
        success: false,
        message:
          'Please check the values set for custom page limit, start number, padding. The values cannot be less than 1',
      }
    }

    if (
      imageSettings.imageDimensionSettings.imageDimension &&
      (imageSettings.imageDimensionSettings.customWidth <= 0 ||
        imageSettings.imageDimensionSettings.customHeight <= 0 ||
        imageSettings.imageDimensionSettings.dpi <= 0)
    ) {
      return {
        success: false,
        message: 'Custom Image Dimension values cannot be less than 1',
      }
    }
    return null
  }

  public selectionChange(
    selectedItems: ImageFileTypeDetailsResponseModel[]
  ): void {
    this.updatedSelection = selectedItems
  }

  public dropdownValueChange(
    dataItem: ImageFileTypeDetailsResponseModel
  ): void {
    this.fileTypeDataUpdated = this.fileTypeDataUpdated.map((setting) =>
      setting.fileTypeGroupId !== dataItem.fileTypeGroupId ? setting : dataItem
    )
  }

  public dropdownClick(event: any): void {
    // prevent dropdown click event from bubbling up to the tree list row click event
    event.stopPropagation()
  }
}
