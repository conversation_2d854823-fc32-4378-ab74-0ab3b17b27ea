import { inject, Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import {
  distinctUntilChanged,
  from,
  map,
  mergeMap,
  switchMap,
  take,
} from 'rxjs'
import * as ProjectActions from './project.actions'
import * as ProjectSelector from './project.selectors'
import { HttpErrorResponse } from '@angular/common/http'
import { ProjectService } from '../../services/project.service'
import { CaseConvertorService } from '@venio/util/utilities'
import { CaseModel } from '@venio/data-access/review'
import {
  CaseDetailModel,
  CaseDetailRequestInfo,
  ResponseModel,
  ReviewSetBatchModel,
  ReviewSetBatchRequestModel,
  ReviewSetDetailRequestModel,
  ReviewSetEntry,
} from '@venio/shared/models/interfaces'
import { select, Store } from '@ngrx/store'
import { ProjectState } from './project.reducer'
import { UuidGenerator } from '@venio/util/uuid'

@Injectable()
export class ProjectEffects {
  private readonly actions$ = inject(Actions)

  private readonly store = inject(Store<ProjectState>)

  private readonly projectService = inject(ProjectService)

  public fetchProjectGroup$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchProjectGroup),
      fetch({
        run: ({ projectId }) => {
          return this.projectService.fetchProjectGroup(projectId).pipe(
            map((projectGroupSuccessResponse) =>
              ProjectActions.fetchProjectGroupSuccess({
                projectGroupSuccessResponse,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const projectGroupErrorResponse = error.error
          return ProjectActions.fetchProjectGroupFailure({
            projectGroupErrorResponse,
          })
        },
      })
    )
  )

  public fetchProjectFolderTreeWithCount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchProjectFolderTreeWithCount),
      fetch({
        run: ({ projectId }) => {
          return this.projectService
            .fetchProjectFolderTreeWithCount(projectId)
            .pipe(
              map((projectFolderTreeWithCountSuccessResponse) =>
                ProjectActions.fetchProjectFolderTreeWithCountSuccess({
                  projectFolderTreeWithCountSuccessResponse,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const projectFolderTreeWithCountErrorResponse = error.error
          return ProjectActions.fetchProjectFolderTreeWithCountFailure({
            projectFolderTreeWithCountErrorResponse,
          })
        },
      })
    )
  )

  public fetchSearchOption$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchSearchOptions),
      distinctUntilChanged((prev, curr) => prev.projectId === curr.projectId),
      fetch({
        run: ({ projectId }) => {
          return this.projectService.fetchSearchOptions(projectId).pipe(
            map((searchOptionSuccessResponse) =>
              ProjectActions.fetchSearchOptionsSuccess({
                searchOptionSuccessResponse,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const searchOptionErrorResponse = error.error
          return ProjectActions.fetchSearchOptionsFailure({
            searchOptionErrorResponse,
          })
        },
      })
    )
  )

  public fetchProject$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchProjects),
      fetch({
        run: () => {
          return this.projectService.fetchProjects().pipe(
            switchMap((response) => {
              const caseConvertWorker = new CaseConvertorService()
              return from(
                caseConvertWorker.convertToCase<CaseModel[]>(
                  response,
                  'camelCase'
                )
              )
            }),
            map((fetchProjectSuccessResponse) =>
              ProjectActions.fetchProjectsSuccess({
                fetchProjectSuccessResponse,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const fetchProjectErrorResponse = error.error
          return ProjectActions.fetchProjectsFailure({
            fetchProjectErrorResponse,
          })
        },
      })
    )
  )

  public fetchProjectInfo$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchProjectInfo),
      fetch({
        run: ({ projectId }) => {
          return this.projectService.fetchProjectInfo(projectId).pipe(
            switchMap((response) => {
              const caseConvertWorker = new CaseConvertorService()
              return from(
                caseConvertWorker.convertToCase<ResponseModel>(
                  response,
                  'camelCase'
                )
              )
            }),
            map((projectInfoSuccessResponse) =>
              ProjectActions.fetchProjectInfoSuccess({
                projectInfoSuccessResponse,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const projectInfoErrorResponse = error.error
          return ProjectActions.fetchProjectInfoFailure({
            projectInfoErrorResponse,
          })
        },
      })
    )
  )

  public fetchCaseDetail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchCaseDetail),
      fetch({
        run: () => {
          return this.store.pipe(
            select(
              ProjectSelector.getStateFromProjectStore('caseDetailRequestInfo')
            ),
            take(1),
            switchMap((existingRequestInfo) =>
              this.projectService.fetchCaseDetail(existingRequestInfo).pipe(
                switchMap((caseDetailResponse) => {
                  // once cases are fetched, we need to first fetch the rights before updating the store.
                  return this.projectService
                    .fetchProjectRights(
                      caseDetailResponse.data?.caseDetailEntries.map(
                        (item: CaseDetailModel) => item.projectId
                      )
                    )
                    .pipe(
                      map((projectRightsResponse) => ({
                        caseDetailResponse,
                        projectRightsResponse,
                      }))
                    )
                }),
                mergeMap(({ caseDetailResponse, projectRightsResponse }) => {
                  const startIndex =
                    (existingRequestInfo.pageNumber - 1) *
                    existingRequestInfo.pageSize

                  const caseDetailsWithSN = (
                    caseDetailResponse.data?.caseDetailEntries || []
                  ).map((item, index) => ({
                    ...item,
                    sn: startIndex + index + 1,
                  }))

                  return [
                    ProjectActions.fetchProjectRightsSuccess({
                      projectRights:
                        projectRightsResponse.data['ProjectIdsByUserRight'],
                    }),
                    ProjectActions.fetchCaseDetailSuccess({
                      caseDetailSuccessResponse: {
                        ...caseDetailResponse.data,
                        caseDetailEntries: caseDetailsWithSN,
                      },
                    }),
                    ProjectActions.updateCaseDetailRequestInfo({
                      caseDetailRequestInfo: {
                        ...existingRequestInfo,
                        totalCaseCount: caseDetailResponse.data.totalCaseCount,
                      } as CaseDetailRequestInfo,
                    }),
                  ]
                })
              )
            )
          )
        },
        onError: (_, error: HttpErrorResponse) => {
          const caseDetailErrorResponse = error.error as ResponseModel
          return ProjectActions.fetchCaseDetailFailure({
            caseDetailErrorResponse,
          })
        },
      })
    )
  )

  public fetchReviewSetSummaryDetail$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchReviewSetSummaryDetail),
      fetch({
        run: ({ type }) => {
          return this.store.pipe(
            select(
              ProjectSelector.getStateFromProjectStore(
                'reviewSetSummaryDetailRequestInfo'
              )
            ),
            take(1),
            switchMap((existingRequestInfo) =>
              this.projectService
                .fetchReviewSetSummaryDetail(existingRequestInfo)
                .pipe(
                  mergeMap((caseDetailResponse) => {
                    const startIndex =
                      (existingRequestInfo.pageNumber - 1) *
                      existingRequestInfo.pageSize

                    const reviewSetSummaryWithSn = (
                      caseDetailResponse.data.reviewSetEntries || []
                    ).map((item: ReviewSetEntry, index: number) => ({
                      ...item,
                      sn: startIndex + index + 1,
                      uuid: UuidGenerator.uuid,
                    }))

                    return [
                      ProjectActions.fetchReviewSetSummaryDetailSuccess({
                        reviewSetSummaryDetailSuccess: {
                          ...caseDetailResponse.data,
                          reviewSetEntries: reviewSetSummaryWithSn,
                        },
                      }),
                      ProjectActions.updateReviewSetSummaryDetailRequestInfo({
                        reviewSetSummaryDetailRequestInfo: {
                          ...existingRequestInfo,
                          totalReviewSetCount:
                            caseDetailResponse.data.totalReviewSetCount,
                        } as ReviewSetDetailRequestModel,
                      }),
                    ]
                  })
                )
            )
          )
        },
        onError: (action, error: unknown) => {
          const reviewSetSummaryDetailError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.fetchReviewSetSummaryDetailFailure({
            reviewSetSummaryDetailError,
          })
        },
      })
    )
  )

  public toggleFavoriteProject = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.toggleFavoriteProject),
      fetch({
        run: ({ projectId, isFavoriteProject }) => {
          return this.projectService
            .toggleFavoriteProject(projectId, isFavoriteProject)
            .pipe(
              map(() =>
                ProjectActions.toggleFavoriteProjectSuccess({
                  projectId,
                  isFavoriteProject,
                })
              )
            )
        },
        onError: ({ projectId }, error: unknown) => {
          return ProjectActions.toggleFavoriteProjectFailure({
            projectId,
          })
        },
      })
    )
  )

  public fetchProjectRight$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchProjectRights),
      fetch({
        run: ({ projectIds }) => {
          return this.projectService.fetchProjectRights(projectIds).pipe(
            map((response) =>
              ProjectActions.fetchProjectRightsSuccess({
                projectRights: response.data,
              })
            )
          )
        },
        onError: (action, error: unknown) => {
          const projectRightErrorResponse = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.fetchProjectRightsFailure({
            projectRightErrorResponse,
          })
        },
      })
    )
  )

  public deleteReviewSet$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.deleteReviewSet),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.projectService
            .deleteReviewSet(projectId, reviewSetId)
            .pipe(
              mergeMap((reviewSetDeleteSuccess) => [
                // Patch the success state of the review set delete.
                ProjectActions.deleteReviewSetSuccess({
                  reviewSetDeleteSuccess,
                }),
                // Once the deletion is done and success, we need to fetch the review set summary detail.
                ProjectActions.fetchReviewSetSummaryDetail(),
              ])
            )
        },
        onError: (_, error: unknown) => {
          const reviewSetDeleteError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.deleteReviewSetFailure({
            reviewSetDeleteError,
          })
        },
      })
    )
  )

  public fetchTaggedDocumentTags$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchTaggedDocumentTags),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.projectService
            .fetchTaggedDocumentTags(projectId, reviewSetId)
            .pipe(
              map((taggedDocumentTagsSuccess) =>
                ProjectActions.fetchTaggedDocumentTagsSuccess({
                  taggedDocumentTagsSuccess,
                })
              )
            )
        },
        onError: (_, error: HttpErrorResponse) => {
          const taggedDocumentTagsError = error.error as ResponseModel
          return ProjectActions.fetchTaggedDocumentTagsFailure({
            taggedDocumentTagsError,
          })
        },
      })
    )
  )

  public fetchReviewSetDocumentView$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchReviewSetDocumentView),
      fetch({
        run: ({ projectId, reviewSetId, payload }) => {
          return this.projectService
            .fetchReviewSetDocumentView(projectId, reviewSetId, payload)
            .pipe(
              map((reviewSetDocumentViewSuccess) =>
                ProjectActions.fetchReviewSetDocumentViewSuccess({
                  reviewSetDocumentViewSuccess,
                })
              )
            )
        },
        onError: (_, error: HttpErrorResponse) => {
          const reviewSetDocumentViewError = error.error as ResponseModel
          return ProjectActions.fetchReviewSetDocumentViewFailure({
            reviewSetDocumentViewError,
          })
        },
      })
    )
  )

  public fetchReviewSetBatch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchReviewSetBatch),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.store.pipe(
            select(
              ProjectSelector.getStateFromProjectStore(
                'reviewSetBatchRequestInfo'
              )
            ),
            take(1),
            switchMap((existingBatchRequestInfo) =>
              this.projectService
                .fetchReviewSetBatch(
                  projectId,
                  reviewSetId,
                  existingBatchRequestInfo
                )
                .pipe(
                  mergeMap((reviewSetBatchResponse) => {
                    const startIndex =
                      (existingBatchRequestInfo.pageNumber - 1) *
                      existingBatchRequestInfo.pageSize

                    const reviewSetBatchWithSn: ReviewSetBatchModel[] = (
                      reviewSetBatchResponse.data.batchViewModelEntries || []
                    ).map((item: ReviewSetBatchModel, index: number) => ({
                      ...item,
                      sn: startIndex + index + 1,
                      uuid: UuidGenerator.uuid,
                    }))

                    return [
                      ProjectActions.fetchReviewSetBatchSuccess({
                        reviewSetBatchSuccess: reviewSetBatchWithSn,
                      }),
                      ProjectActions.updateReviewSetBatchRequestInfo({
                        reviewSetBatchRequestInfo: {
                          ...existingBatchRequestInfo,
                          totalReviewSetBatchCount:
                            reviewSetBatchResponse.data.totalBatchCount,
                        } as ReviewSetBatchRequestModel,
                      }),
                    ]
                  })
                )
            )
          )
        },
        onError: (action, error: unknown) => {
          const reviewSetBatchError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.fetchReviewSetBatchFailure({
            reviewSetBatchError,
          })
        },
      })
    )
  )

  public fetchReviewSetUserGroups$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchReviewSetUserGroups),
      fetch({
        run: ({ projectId, reviewSetId }) => {
          return this.projectService
            .fetchReviewSetUserAndUserGroups(projectId, reviewSetId)
            .pipe(
              map((response) =>
                ProjectActions.fetchReviewSetUserGroupsSuccess({
                  reviewSetUserGroupSuccess: response.data,
                })
              )
            )
        },
        onError: (action, error: unknown) => {
          const reviewSetUserGroupError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.fetchReviewSetUserGroupsFailure({
            reviewSetUserGroupError,
          })
        },
      })
    )
  )

  public reassignReviewSetBatch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.reassignReviewSetBatch),
      fetch({
        run: ({ projectId, reviewSetId, batchId, reviewerId }) => {
          return this.projectService
            .reassignBatch(projectId, reviewSetId, batchId, reviewerId)
            .pipe(
              mergeMap((reviewSetReassignSuccess) => [
                ProjectActions.reassignReviewSetBatchSuccess({
                  reviewSetReassignSuccess,
                }),
                // Once the reassign process is done and success, we need to fetch the review set batch list.
                ProjectActions.fetchReviewSetBatch({ projectId, reviewSetId }),
              ])
            )
        },
        onError: (_, error: unknown) => {
          const reviewSetReassignError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.reassignReviewSetBatchFailure({
            reviewSetReassignError,
          })
        },
      })
    )
  )

  public rebatchReviewSetBatch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.rebatchReviewSetBatch),
      fetch({
        run: ({ projectId, reviewSetId, batchIds }) => {
          return this.projectService
            .purgeBatches(projectId, reviewSetId, batchIds)
            .pipe(
              mergeMap((reviewSetRebatchSuccess) => [
                ProjectActions.rebatchReviewSetBatchSuccess({
                  reviewSetRebatchSuccess,
                }),
                // Once the rebatch process is done and success, we need to fetch the review set batch list.
                ProjectActions.fetchReviewSetBatch({ projectId, reviewSetId }),
              ])
            )
        },
        onError: (_, error: unknown) => {
          const reviewSetReBatchError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.rebatchReviewSetBatchFailure({
            reviewSetReBatchError,
          })
        },
      })
    )
  )

  public deleteReviewSetBatch$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.deleteReviewSetBatch),
      fetch({
        run: ({ projectId, reviewSetId, batchIds }) => {
          return this.projectService
            .deleteBatches(projectId, reviewSetId, batchIds)
            .pipe(
              mergeMap((reviewSetDeleteBatchSuccess) => [
                ProjectActions.deleteReviewSetBatchSuccess({
                  reviewSetDeleteBatchSuccess,
                }),
                // Once the deletion process is done and success, we need to fetch the review set batch list.
                ProjectActions.fetchReviewSetBatch({ projectId, reviewSetId }),
              ])
            )
        },
        onError: (_, error: unknown) => {
          const reviewSetDeleteBatchError = (error as HttpErrorResponse)
            .error as ResponseModel
          return ProjectActions.deleteReviewSetBatchFailure({
            reviewSetDeleteBatchError,
          })
        },
      })
    )
  )

  public checkUploadInvitationAccess$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.checkUploadInvitationAccess),
      fetch({
        run: ({ projectId, userWiseToken }) => {
          return this.projectService
            .checkUploadInvitationAccess(projectId, userWiseToken)
            .pipe(
              map((response) =>
                ProjectActions.checkUploadInvitationAccessSuccess({
                  response: response,
                })
              )
            )
        },
      })
    )
  )

  public fetchProjectMediaStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(ProjectActions.fetchUnIndexMediaStatus),
      fetch({
        id: ({ projectId }) => projectId,
        run: ({ projectId }) => {
          return this.projectService.fetchUnIndexMedia(projectId).pipe(
            map((unIndexMediaSuccessResponse) =>
              ProjectActions.fetchUnIndexMediaStatusSuccess({
                unIndexMediaSuccessResponse,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const unIndexMediaErrorResponse = error.error
          return ProjectActions.fetchUnIndexMediaStatusFailure({
            unIndexMediaErrorResponse,
          })
        },
      })
    )
  )
}
