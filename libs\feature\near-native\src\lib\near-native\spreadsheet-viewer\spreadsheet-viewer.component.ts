import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  signal,
  SimpleChanges,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  EMPTY,
  Subject,
  catchError,
  from,
  switchMap,
  take,
  takeUntil,
} from 'rxjs'
import { NearNativeFacade } from '../../services/near-native.facade'

import { LoaderModule } from '@progress/kendo-angular-indicators'

import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { VenioNotificationService } from '@venio/feature/notification'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  SpreadsheetComponent,
  SpreadsheetModule,
} from '@syncfusion/ej2-angular-spreadsheet'
import {
  DocumentExcelResponse,
  SpreadsheetAction,
} from '../../models/near-native.model'
import { ResponseModel } from '@venio/shared/models/interfaces'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { registerLicense } from '@syncfusion/ej2-base'
import {
  IndexedDBHandlerService,
  SpreadsheetPartModel,
  UserRights,
} from '@venio/data-access/review'

import { HttpErrorResponse } from '@angular/common/http'
import {
  ReviewPanelFacade,
  UpdateDocumentHistoryPage,
} from '@venio/data-access/document-utility'
@Component({
  selector: 'lib-spreadsheet-viewer',
  standalone: true,
  imports: [
    CommonModule,
    SpreadsheetModule,
    LoaderModule,
    InputsModule,
    LabelModule,
    ButtonsModule,
    SvgLoaderDirective,
    UserGroupRightCheckDirective,
  ],
  templateUrl: './spreadsheet-viewer.component.html',
  styleUrls: ['./spreadsheet-viewer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SpreadsheetViewerComponent implements OnChanges, OnDestroy {
  @Input()
  public fileId: number

  @Input()
  public projectId: number

  private toDestroy$: Subject<void> = new Subject<void>()

  public showSpinner: boolean

  public errorMessage: string

  public showToolbars: boolean

  public SpreadsheetAction = SpreadsheetAction

  @ViewChild('spreadsheet', { static: false })
  public spreadsheetObj: SpreadsheetComponent

  public highlightButtonStatus = false

  /** current active index*/
  private activeSheet: number

  /** current selected cell range */
  private selectedCell: any

  public get hasAnnotation(): boolean {
    return (
      this.annotaitonObject?.Root?.highlightIndex ||
      this.annotaitonObject?.Root?.sheetIndex
    )
  }
  /**annotation object of the current document*/
  private annotaitonObject: { Root: any }
  /**current active cell*/
  private activeSheetCellRedactObj = {}

  /**current active highlight cell*/
  private activeSheetCellHighlightObj = {}

  /**array stores the set of redacted cell indexl*/
  private redactedIndex = []

  /**array stores the set of highlight cell indexl*/
  private highlightIndex = []

  public UserRights = UserRights
  public originalSpreadSheetContent = signal('')
  private isAnnotationHidden = signal(false)
  public get annotationsHidden(): boolean {
    return this.isAnnotationHidden()
  }

  constructor(
    private nearNativeFacade: NearNativeFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private notification: VenioNotificationService,
    private indexDb: IndexedDBHandlerService,
    private cdr: ChangeDetectorRef
  ) {
    registerLicense(
      'ORg4AjUWIQA/Gnt2UVhhQlVFfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTX5Sdk1jWn5Zc3RUQWdY'
    )
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes?.fileId?.currentValue) {
      this.showSpinner = true
      this.fetchSpreadsheetDocument()
    }
  }

  public fetchSpreadsheetDocument(): void {
    this.annotaitonObject = null
    this.activeSheetCellRedactObj = []
    this.activeSheetCellHighlightObj = []
    this.redactedIndex = []
    this.highlightIndex = []
    from(
      this.indexDb.ifSpecificPartExistsInSpreadSheet(
        this.projectId,
        this.fileId
      )
    )
      .pipe(
        switchMap((exists) => {
          if (exists) {
            return this.nearNativeFacade
              .fetchExcelAnnotation(this.projectId, this.fileId)
              .pipe(
                switchMap((response: ResponseModel) => {
                  this.annotaitonObject = response?.data
                  return from(
                    this.indexDb
                      .getSpreadsheetParts(this.projectId, this.fileId)
                      .then((result: SpreadsheetPartModel[]) => {
                        return result[0].Workbook
                      })
                  )
                })
              )
          }
          return this.nearNativeFacade
            .fetchExcelWorkBook(this.projectId, this.fileId)
            .pipe(
              switchMap((response: ResponseModel) => {
                const documentExcelWorkbook: DocumentExcelResponse =
                  response.data
                this.annotaitonObject = documentExcelWorkbook.RedactedFile
                this.indexDb.addSpreadsheetParts([
                  {
                    ProjectId: this.projectId,
                    FileId: this.fileId,
                    Workbook: documentExcelWorkbook.WorkBook,
                  },
                ])
                return from([documentExcelWorkbook.WorkBook])
              })
            )
        }),
        catchError((error: unknown) => {
          this.showSpinner = false
          this.handleDocumentFetchError(error)
          return EMPTY
        }),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((workbook) => {
        this.cdr.markForCheck()
        this.showSpinner = false
        this.errorMessage = null
        this.loadExcelContent(workbook)
      })
  }

  private loadExcelContent(content: string): void {
    try {
      this.originalSpreadSheetContent.set(content)
      const json = JSON.parse(content)
      if (json.Workbook) {
        json.Workbook.activeSheetIndex = 0
      }
      this.spreadsheetObj.openFromJson({ file: json, triggerEvent: true })
      setTimeout(() => {
        this.onOpenComplete(null)
      }, 100)
    } catch (error: any) {
      this.cdr.markForCheck()
      this.errorMessage = 'Error occurred when loading the document'
      console.error('Error', error)
    }
  }

  public onOpenComplete($event): void {
    this.openAnnotation()
    this.registerSheetChangedEvent()
  }

  private registerSheetChangedEvent(): void {
    Object.assign(
      [],
      document.getElementsByClassName('e-toolbar-item e-template')
    ).forEach((el) => {
      el.addEventListener('click', (event) => {
        setTimeout(() => {
          this.selectedCell = null
          this.openAnnotation()
        }, 0)
      })
    })
  }

  private openAnnotation(): void {
    if (!this.annotaitonObject) return
    this.activeSheetCellRedactObj = this.annotaitonObject.Root.sheetIndex
    this.activeSheetCellHighlightObj = this.annotaitonObject.Root.highlightIndex

    this.activeSheetEventRedaction()
    this.activeSheetEventHighlight()
    if (this.annotaitonObject) {
      this.annotaitonObject.Root.highlightIndex &&
        (this.indexAssign('highlight'),
        this.activeSheetAnnotation(this.highlightIndex, 'highlight'))

      this.annotaitonObject.Root.sheetIndex &&
        (this.indexAssign('redact'),
        this.activeSheetAnnotation(this.redactedIndex, 'redact'))
      //this.loadRedacted = true
    }
  }

  /**
   *this method is called upon to redact active sheet
   * @constructor
   */
  private activeSheetEventRedaction(): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    if (this.activeSheetCellRedactObj) {
      this.redactedIndex =
        this.activeSheet in this.activeSheetCellRedactObj
          ? this.activeSheetCellRedactObj[
              this.spreadsheetObj.activeSheetIndex.toString()
            ]
          : (this.redactedIndex = [])
    } else {
      this.redactedIndex = []
    }
  }

  /**
   *annotaiton object is fetched from the server and the JSOn is passed to this method which applies annotation to the opening excel
   * @constructor
   * * @param {string} cellIndex - cell index for annotation
   * * @param {string} event - stype of annotation
   */
  private activeSheetAnnotation(cellIndex, type): void {
    if (type === 'highlight') {
      for (let x = 0; x < cellIndex.length; x++) {
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'yellow' },
          cellIndex[x]
        )
      }
      return
    }
    for (let x = 0; x < cellIndex.length; x++) {
      this.spreadsheetObj.cellFormat({ backgroundColor: 'black' }, cellIndex[x])
      this.spreadsheetObj.cellFormat({ color: 'black' }, cellIndex[x])
    }
    return
  }

  /**
   *this method is called upon to highlight active sheet
   * @constructor
   */
  private activeSheetEventHighlight(): void {
    this.activeSheet = this.spreadsheetObj.activeSheetIndex
    if (this.activeSheetCellHighlightObj) {
      this.highlightIndex =
        this.activeSheet in this.activeSheetCellHighlightObj
          ? this.activeSheetCellHighlightObj[
              this.spreadsheetObj.activeSheetIndex.toString()
            ]
          : (this.highlightIndex = [])
    } else {
      this.highlightIndex = []
    }
  }

  /**
   *this method is called upon to unmutate the redaction arrays i.e. hightlightArray and redactionArray
   * @constructor
   * * @param {string} type - type of annotation
   */
  private indexAssign(type: string): void {
    if (type === 'highlight') {
      this.highlightIndex =
        typeof this.highlightIndex === 'string'
          ? [...[this.highlightIndex]]
          : Object.assign([], this.highlightIndex)
    } else {
      this.redactedIndex =
        typeof this.redactedIndex === 'string'
          ? [...[this.redactedIndex]]
          : Object.assign([], this.redactedIndex)
    }
  }

  private handleDocumentFetchError(error: unknown): void {
    this.cdr.markForCheck()
    const err = error as HttpErrorResponse
    if (
      err.error.message
        .toLowerCase()
        .includes('workbook is protected and password')
    ) {
      this.errorMessage = 'Spreadsheet is protected'
    } else if (
      err.error.message.toLowerCase().includes(' incorrect password\r\n   at')
    ) {
      this.errorMessage = 'Incorrect password'
    } else if (
      err.error.message.toLowerCase().includes('file is not supported') ||
      err.error.message
        .toLowerCase()
        .includes(' Cannot recognize current file type')
    ) {
      this.errorMessage = 'Selected document is not supported'
    } else if (
      err.error.message
        .toLowerCase()
        .includes('because it is being used by another process')
    ) {
      this.errorMessage = 'Document is being used by other process'
    } else if (
      err.error.message.toLowerCase().includes('could not find file')
    ) {
      this.errorMessage = 'Selected document not found'
    } else {
      this.errorMessage = 'Excel document content cannot be requested '
    }
  }

  public actionButtonHandler(action: SpreadsheetAction): void {
    switch (action) {
      case SpreadsheetAction.HIGHLIGHT:
        this.actionSelection('highlight')
        break
      case SpreadsheetAction.ENABLE_REDACTION:
        this.showToolbars = !this.showToolbars
        break
      case SpreadsheetAction.REDACT:
        this.actionSelection('redaction')
        break
      case SpreadsheetAction.UNREDACT:
        this.UnredactAction()
        break
      case SpreadsheetAction.SAVE:
        this.saveAction()
        break
      default:
        break
    }
  }

  public UnredactAction(): void {
    const index = this.redactedIndex.findIndex(
      (item) => item === this.selectedCell
    )
    const id =
      this.selectedCell.split(':')[0] + ':' + this.selectedCell.split(':')[0]
    let singleCellIndex = 0
    if (index === -1) {
      singleCellIndex = this.redactedIndex.findIndex((item) => item === id)
    }
    if (singleCellIndex === -1) {
      this.wholeCellUnRedact('highlight')
      return
    }
    this.wholeCellUnRedact('redaction')
  }

  private saveAction(): void {
    const saveSetting = {
      sheetIndex: { ...this.activeSheetCellRedactObj },
      highlightIndex: { ...this.activeSheetCellHighlightObj },
    }
    const requestModel = {
      projectId: this.projectId,
      fileId: this.fileId,
    }
    const isRedaction: boolean =
      Object.keys(saveSetting.sheetIndex).length !== 0 ||
      Object.keys(saveSetting.highlightIndex).length !== 0
    this.nearNativeFacade
      .saveExcelAnnotation(saveSetting, requestModel, isRedaction)
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe({
        next: (res: ResponseModel) => {
          this.reviewPanelFacade.refrehDocumentHistory(
            UpdateDocumentHistoryPage.UPDATE_PDF_REDACTION
          )
          this.notification.showSuccess('Annotation saved successfully')
        },
        error: (err: unknown) =>
          this.notification.showError(
            (err as HttpErrorResponse).error?.message
          ),
      })
  }

  public selectCell(event: any): void {
    this.selectedCell = event.range
    this.annotationStatusBtn(event.range)
  }

  private actionSelection(type: string): void {
    const {
      el,
      selectedRangeSplit,
      firstSelectedArray,
      secondSelectedSplits,
      cellRange,
    } = this.cellIndexSplit()
    selectedRangeSplit[0] !== selectedRangeSplit[1]
      ? cellRange[0] !== cellRange[2]
        ? this.cellRangeRedaction(cellRange, type)
        : this.wholeCell(
            firstSelectedArray[1],
            secondSelectedSplits[0],
            secondSelectedSplits[1],
            type,
            el && el.value.includes('='),
            false
          )
      : this.cellAssign(type)
  }

  private wholeCell(
    first: string,
    character: string,
    last: string,
    type: string,
    isDependend: boolean,
    isRemove?: boolean
  ): void {
    const key = this.spreadsheetObj.activeSheetIndex.toString()
    const firstInt = parseInt(first)
    const lastInt = parseInt(last)
    const startKey = firstInt < lastInt ? firstInt : lastInt
    const endKey = startKey === firstInt ? lastInt : firstInt
    for (let i = startKey; i <= endKey; i++) {
      const ranges = character + i + ':' + character + i
      if (isRemove === true) {
        const highlightSelectedIndex = this.highlightIndex.indexOf(
          this.selectedCell
        )
        const redactSelectedIndex = this.redactedIndex.indexOf(
          this.selectedCell
        )
        if (i === startKey) {
          type === 'highlight'
            ? highlightSelectedIndex !== -1 &&
              this.removeHighLight(this.selectedCell)
            : redactSelectedIndex !== -1 &&
              this.removeRedaction(this.selectedCell)
        }
        type === 'highlight'
          ? this.removeHighLight(ranges)
          : this.removeRedaction(ranges)
      } else {
        /**For dependent cells */
        if (i === startKey && isDependend) {
          this.cellObjAssign(type, this.selectedCell, key)
        }
        this.annotationStatusBtn(ranges)
        this.cellObjAssign(type, ranges, key)
      }
    }
  }

  private cellAssign(type: string): void {
    const key = this.spreadsheetObj.activeSheetIndex.toString()
    this.annotationStatusBtn(this.selectedCell)
    this.cellObjAssign(type, this.selectedCell, key)
  }

  /**
   *this method is called upon the removing of the highlight annotation on cell
   * @constructor
   * * @param {string} range - cell index for removing highlight
   */
  private removeHighLight(range?: string): void {
    if (!this.highlightIndex.includes(range)) {
      return
    }
    this.spreadsheetObj.cellFormat({ backgroundColor: 'white' }, range)
    const index = this.highlightIndex.findIndex((item) => item === range)
    this.highlightIndex.splice(index, 1)
    if (
      this.activeSheetCellHighlightObj[
        this.spreadsheetObj.activeSheetIndex
      ].includes(range)
    ) {
      typeof this.activeSheetCellHighlightObj[
        this.spreadsheetObj.activeSheetIndex
      ] === 'string'
        ? (this.activeSheetCellHighlightObj[
            this.spreadsheetObj.activeSheetIndex
          ] = [])
        : this.activeSheetCellHighlightObj[
            this.spreadsheetObj.activeSheetIndex
          ].splice(index, 1)
    }
    this.highlightIndex = this.filterArray(this.highlightIndex)
    this.annotationStatusBtn(range)
  }

  /**
   *this method is called upon the removing empty element from the array
   * @constructor
   * * @param {} testArray - array to remove empty element
   */
  private filterArray(testArray): Array<any> {
    let index = -1
    const arrayLength = testArray ? testArray.length : 0
    let resIndex = -1
    const result = []

    while (++index < arrayLength) {
      const value = testArray[index]

      if (value) {
        result[++resIndex] = value
      }
    }

    return result
  }

  /**
   *this method is called upon to selected cell range to array based on type of annotation
   * @constructor
   * * @param {string} type - type of annotation
   * * @param {string} range - range of the cell to be redacted
   * * @param {number} key - active sheet
   */
  private cellObjAssign(type, ranges, key): void {
    if (this.highlightButtonStatus) {
      return
    }
    if (type === 'highlight') {
      this.highlightIndex.indexOf(ranges) === -1 &&
        (this.highlightIndex.push(ranges),
        this.spreadsheetObj.cellFormat({ backgroundColor: 'yellow' }, ranges))
      if (!this.activeSheetCellHighlightObj) {
        this.activeSheetCellHighlightObj = {}
      }
      this.activeSheetCellHighlightObj[key] = this.highlightIndex
    } else {
      this.redactedIndex.indexOf(ranges) === -1 &&
        (this.redactedIndex.push(ranges),
        this.spreadsheetObj.cellFormat(
          { backgroundColor: 'black', color: 'black' },
          ranges
        ))
      if (!this.activeSheetCellRedactObj) {
        this.activeSheetCellRedactObj = {}
      }
      this.activeSheetCellRedactObj[key] = this.redactedIndex
    }
  }

  /**
   *method to check the annotation status of the current cell
   * @constructor
   * * @param {} selectedCell - selected cell range
   */
  private annotationStatusBtn(selectedCell: string): void {
    const splitCell = selectedCell.split(':')
    const redactIndex = this.redactedIndex.includes(
      splitCell[0] !== splitCell[1]
        ? splitCell[0] + ':' + splitCell[0]
        : selectedCell
    )
    this.highlightButtonStatus = redactIndex
  }

  /**
   *this method is called upon the removing of the redaction on cell
   * @constructor
   * * @param {string} range - cell index for removing redaction
   */
  private removeRedaction(range: string): void {
    if (!this.redactedIndex.includes(range)) {
      return
    }
    const index = this.redactedIndex.findIndex((item) => item === range)
    this.redactedIndex.splice(index, 1)
    if (
      this.activeSheetCellRedactObj[
        this.spreadsheetObj.activeSheetIndex
      ].includes(range)
    ) {
      typeof this.activeSheetCellRedactObj[
        this.spreadsheetObj.activeSheetIndex
      ] === 'string'
        ? (this.activeSheetCellRedactObj[this.spreadsheetObj.activeSheetIndex] =
            [])
        : this.activeSheetCellRedactObj[
            this.spreadsheetObj.activeSheetIndex
          ].splice(index, 1)
    }
    this.redactedIndex = this.filterArray(this.redactedIndex)
    this.spreadsheetObj.cellFormat({ backgroundColor: 'white' }, range)
    this.applyHighlightOnRedactionRemove(range)
    this.annotationStatusBtn(range)
  }

  /**
   *apply highlight to cell which has both redaction and highligt on redaction remove
   * @constructor
   */
  private applyHighlightOnRedactionRemove(index): void {
    const includes = this.highlightIndex.includes(index)
    includes &&
      this.spreadsheetObj.cellFormat({ backgroundColor: 'yellow' }, index)
  }

  private cellIndexSplit(): any {
    const el: any = document.getElementsByClassName('e-formula-bar')[0]
    const selectedRangeSplit =
      el.value.includes('=') && el.value.includes(':')
        ? el.value.split('(')[1].split(')')[0].split(':')
        : this.selectedCell.split(':')
    const firstSelectedArray = selectedRangeSplit[0].match(/[a-z]+|[^a-z]+/gi)
    const secondSelectedSplits = selectedRangeSplit[1].match(/[a-z]+|[^a-z]+/gi)
    const cellRange = this.selectedCell.match(/[a-z]+|[^a-z]+/gi)
    return {
      el,
      selectedRangeSplit,
      firstSelectedArray,
      secondSelectedSplits,
      cellRange,
    }
  }

  private cellRangeRedaction(
    cellRange,
    type: string,
    removeType?: boolean
  ): void {
    let currentChar =
      cellRange[0].charCodeAt(0) < cellRange[2].charCodeAt(0)
        ? cellRange[0]
        : cellRange[2]
    const lastChar = currentChar === cellRange[0] ? cellRange[2] : cellRange[0]
    const first =
      parseInt(cellRange[1].split(':')[0]) < parseInt(cellRange[3])
        ? cellRange[1].split(':')[0]
        : cellRange[3]
    const last =
      parseInt(first) === parseInt(cellRange[1].split(':')[0])
        ? cellRange[3]
        : cellRange[1].split(':')[0]
    const key = this.spreadsheetObj.activeSheetIndex.toString()
    /** spread sheet cells range can be to A1:AB2
     * firt if condition checks whether the cell row range to to A-Z
     * if the first condition is true than while loop will be initiated untill the cell range is z100:z100
     */
    if (lastChar.length === 1) {
      while (currentChar !== String.fromCharCode(lastChar.charCodeAt(0) + 1)) {
        let i = 0
        for (i = parseInt(first); i <= parseInt(last); i++) {
          const ranges = currentChar + i + ':' + currentChar + i
          if (removeType === true) {
            type === 'highlight'
              ? this.removeHighLight(ranges)
              : this.removeRedaction(ranges)
          } else {
            this.annotationStatusBtn(ranges)
            this.cellObjAssign(type, ranges, key)
          }
        }
        currentChar = String.fromCharCode(currentChar.charCodeAt(0) + 1)
      }
    } else {
      /**
       * if the cell range is from A1-CD100 than else will be executed
       *as ever cell ranges till Z i.e Z1:Z100,BZ1:BZ100
       * this will loop will get executes until the end range is reached
       */
      while (currentChar !== String.fromCharCode('Z'.charCodeAt(0) + 1)) {
        for (let i = parseInt(first); i <= parseInt(last); i++) {
          const ranges = currentChar + i + ':' + currentChar + i
          if (removeType === true) {
            type === 'highlight'
              ? this.removeHighLight(ranges)
              : this.removeRedaction(ranges)
          } else {
            this.annotationStatusBtn(ranges)
            this.cellObjAssign(type, ranges, key)
          }
        }
        if (currentChar === 'Z') {
          const items = lastChar.split('')
          let begin = 'A'
          while (begin !== String.fromCharCode(items[0].charCodeAt(0) + 1)) {
            let end = 'A'
            while (
              begin === items[0]
                ? end !== String.fromCharCode(items[1].charCodeAt(0) + 1)
                : end !== String.fromCharCode('Z'.charCodeAt(0) + 1)
            ) {
              for (let i = parseInt(first); i <= parseInt(last); i++) {
                const ranges = begin + end + i + ':' + begin + end + i
                if (removeType === true) {
                  type === 'highlight'
                    ? this.removeHighLight(ranges)
                    : this.removeRedaction(ranges)
                } else {
                  this.annotationStatusBtn(ranges)
                  this.cellObjAssign(type, ranges, key)
                }
              }
              end = String.fromCharCode(end.charCodeAt(0) + 1)
            }
            begin = String.fromCharCode(begin.charCodeAt(0) + 1)
          }
        }
        currentChar = String.fromCharCode(currentChar.charCodeAt(0) + 1)
      }
    }
  }

  private wholeCellUnRedact(type): void {
    const {
      el,
      selectedRangeSplit,
      firstSelectedArray,
      secondSelectedSplits,
      cellRange,
    } = this.cellIndexSplit()
    if (
      selectedRangeSplit[0] !== selectedRangeSplit[1] &&
      cellRange[0] === cellRange[2]
    ) {
      this.wholeCell(
        firstSelectedArray[1],
        secondSelectedSplits[0],
        secondSelectedSplits[1],
        type,
        el && el.value.includes('='),
        true
      )
    } else if (
      selectedRangeSplit[0] !== selectedRangeSplit[1] &&
      cellRange[0] !== cellRange[2]
    ) {
      this.cellRangeRedaction(cellRange, type, true)
    } else {
      type === 'highlight'
        ? this.removeHighLight(this.selectedCell)
        : this.removeRedaction(this.selectedCell)
    }
  }

  public ShowHideAnnotation(): void {
    if (!this.originalSpreadSheetContent()) return
    this.isAnnotationHidden.update((value) => !value)
    const json = JSON.parse(this.originalSpreadSheetContent())
    if (json.Workbook) {
      json.Workbook.activeSheetIndex = 0
    }
    this.spreadsheetObj.openFromJson({ file: json, triggerEvent: true })
    if (!this.isAnnotationHidden()) {
      setTimeout(() => {
        this.onOpenComplete(null)
      }, 100)
    }
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
