// Base Class for Default Handling
export class BaseModel {
  constructor(data: Partial<any> = {}) {
    Object.assign(this, data)
    this.ensureDefaults()
  }

  public ensureDefaults(): void {
    // Override in derived classes to ensure defaults for missing fields
  }
}

// ColorConversion Model
export class ColorConversion extends BaseModel {
  public imageFileType = '1'

  public pdfFiles = '1'

  public powerpoint = '1'

  public override ensureDefaults(): void {
    this.imageFileType = this.imageFileType ?? '1'
    this.pdfFiles = this.pdfFiles ?? '1'
    this.powerpoint = this.powerpoint ?? '1'
  }
}

// ImageConversionOption Model
export class ImageConversionOption extends BaseModel {
  public imageType = 2

  public imageColorConversion: ColorConversion = new ColorConversion()

  public passwordList: any[] = []

  public deduplicationOption = 'NONE'

  // public timeZone = 'Etc/GMT'

  public csV_Excel_option = 0

  public autoGenerateImagesAfterIngestion = true

  // public ignoreAutoTiffJobsForMediaProcessingStatus = false

  public override ensureDefaults(): void {
    this.imageColorConversion = new ColorConversion(this.imageColorConversion)
  }
}

// ServiceRequestType Model
export class ServiceRequestType extends BaseModel {
  public exportTemplateName: string | null = null

  public serviceType: string | null = null

  public override ensureDefaults(): void {
    this.exportTemplateName = this.exportTemplateName ?? null
    this.serviceType = this.serviceType ?? null
  }
}

// EditableCustomField Model
export class EditableCustomField extends BaseModel {
  public fieldId: any = null

  public fieldName: string | null = null

  public description: string | null = null

  public fieldValue: any = null
}

// SelectiveEndorsementSetting Model
export class SelectiveEndorsementSetting extends BaseModel {
  public tagName: string | null = null

  public tagId: any = null

  public endorsementType: string | null = null

  public endorsementText: string | null = null

  public endorsementLocation = 3

  public override ensureDefaults(): void {
    this.endorsementLocation = this.endorsementLocation ?? 3
  }
}

// ControlNumberSetting Model
export class ControlNumberSetting extends BaseModel {
  public prefix = ''

  public prefixDelimiter = 0

  public startNumber = 1

  public endorseControlNumber = false

  public controlNumberLocation = 5

  public endorseOptionalMessage = false

  public messageText = ''

  public optionalMessageLocation = 3

  public volumnId = ''

  public paddingLength = 8

  public continueFromPreviousControlNumber = false

  public advancedEndorsementSetting: any = null

  public prefixDelimiterValue = ''

  public override ensureDefaults(): void {
    this.prefixDelimiter = this.prefixDelimiter ?? 0
  }
}

// ControlNumberEndorsement Model
export class ControlNumberEndorsement extends BaseModel {
  public sortOrder = 'RELATIVE_FILE_PATH'

  public controlNumberSetting: ControlNumberSetting = new ControlNumberSetting(
    {}
  )

  public exportLocation = 'VoDR_HOME'

  public override ensureDefaults(): void {
    this.controlNumberSetting = new ControlNumberSetting(
      this.controlNumberSetting
    )
  }
}

// FilterOptions Model
export class FilterOptions extends BaseModel {
  public excludeProducedDocuments = false

  public excludeNativeForRedactedDocuments = false
}

// SavedSearchItem Model
export class SavedSearchItem extends BaseModel {
  public searchId: any = null

  public searchName: string | null = null

  public customFieldId: any = null

  public customFieldName: string | null = null
}

// ProductionOptions Model
export class ProductionOptions extends BaseModel {
  public filterOptions: FilterOptions = new FilterOptions({})

  public fieldTemplateId = 10

  public savedSearchesForExpressions: any = null

  public relativityFieldMappingTemplateId?: number = 1

  public connector?: {
    id: number | null
    name: string
    connectorPlatform: string
    userEnvironmentId: number | null
    workspaceId: number | null
    workspaceName: string
    connectorFileSharePath: string
    baseAPIUrl: string
  } = {
    id: null,
    name: '',
    connectorPlatform: '',
    userEnvironmentId: null,
    workspaceId: null,
    workspaceName: '',
    connectorFileSharePath: '',
    baseAPIUrl: '',
  }

  public override ensureDefaults(): void {
    this.filterOptions = new FilterOptions(this.filterOptions)
  }
}

// PrintBinding Model
export class PrintBinding extends BaseModel {
  public bindingType = 0

  public binderSize = 0

  public binderColor = 0
}

// PrintSet Model
export class PrintSet extends BaseModel {
  public printSetOption = 0

  public numberOfSetValue = 1
}

// PrintServiceOption Model
export class PrintServiceOption extends BaseModel {
  public printBinding: PrintBinding = new PrintBinding({})

  public printSet: PrintSet = new PrintSet({})

  public paperType = 0

  public familyFileHandling = 0

  public documentSeparator = 0

  public paperSide = 0

  public override ensureDefaults(): void {
    this.printBinding = new PrintBinding(this.printBinding)
    this.printSet = new PrintSet(this.printSet)
  }
}

// PDFServiceOption Model
export class PDFServiceOption extends BaseModel {
  public pdfType = 0

  public pdfFamilyFileHandling = 0

  public pdfFileNamingConvention = 0
}

// ThirdPartyBillingInformation Model
export class ThirdPartyBillingInformation extends BaseModel {
  public thirdPartyBillingEnabled = false

  public company: string | null = null

  public billingAddress: string | null = null

  public billingCaseName: string | null = null

  public contactPerson: string | null = null

  public contactPhone: string | null = null

  public contactEmail: string | null = null
}

// SettingsInfo Model
export class SettingsInfo extends BaseModel {
  public searchTerm: string | null = null

  public tzTimeZone = 'Etc/GMT'

  public enableNativeFileHandling = false

  public serviceRequestType = 1

  public exportTemplateName = 'PDF Service'

  public imageConversionOption: ImageConversionOption =
    new ImageConversionOption({})

  public controlNumber_Endorsement: ControlNumberEndorsement =
    new ControlNumberEndorsement({})

  public productionOptions: ProductionOptions = new ProductionOptions({})

  public printServiceOption: PrintServiceOption = new PrintServiceOption({})

  public pdfServiceOption: PDFServiceOption = new PDFServiceOption({})

  public thirdPartyBillingOption: ThirdPartyBillingInformation =
    new ThirdPartyBillingInformation({})

  public webURL = 'http://localhost/VenioWeb'

  public clientMatterNo = ''

  public caseName = ''

  public createImage = true

  public dataRetentionRequest = 0

  public editableCustomFieldList: any[] = []

  public productionSourceId = ''

  public enableDiscoveryExceptionHandling = true

  public indexOnlyCase = true

  public autoQueueForEntityExtraction = false

  public ocrLanguages: any = null

  public projectTemplateId = 0

  public transcribeEngine: any = null

  public autoQueueTranscribe = false

  public supportedFiles: any = null

  public autoFolderRelativePathDuringIngestion = false

  public ingestionEngine = 0

  public override ensureDefaults(): void {
    this.searchTerm = this.searchTerm ?? null
    this.tzTimeZone = this.tzTimeZone ?? 'Etc/GMT'
    this.enableNativeFileHandling = this.enableNativeFileHandling ?? false
    this.serviceRequestType = this.serviceRequestType ?? 1
    this.exportTemplateName = this.exportTemplateName ?? 'PDF Service'
    this.imageConversionOption = new ImageConversionOption(
      this.imageConversionOption
    )
    this.controlNumber_Endorsement = new ControlNumberEndorsement(
      this.controlNumber_Endorsement
    )
    this.productionOptions = new ProductionOptions(this.productionOptions)
    this.printServiceOption = new PrintServiceOption(this.printServiceOption)
    this.pdfServiceOption = new PDFServiceOption(this.pdfServiceOption)
    this.thirdPartyBillingOption = new ThirdPartyBillingInformation(
      this.thirdPartyBillingOption
    )
  }
}

// VODRSettings Model
export class VODRSettings extends BaseModel {
  public uploadedFileList: any[] = []

  public settingsInfo: SettingsInfo = new SettingsInfo({})

  public addDataToExistingCase: any = null

  public overrideSettingsInfo: any = null

  public isContinuedAfterControlNumberConflict: any = null

  public override ensureDefaults(): void {
    this.settingsInfo = new SettingsInfo(this.settingsInfo)
  }
}

// ExportFieldTemplate Model
export class ExportFieldTemplate extends BaseModel {
  public id: any = null

  public isDefaultTemplate: boolean | null = null

  public isEditable: boolean | null = null

  public name: string | null = null

  public templateProjectId: any = null
}

// ExportFieldDetails Model
export class ExportFieldDetails extends BaseModel {
  public description: string | null = null

  public fieldName: string | null = null

  public groupName: string | null = null

  public productionFieldName: string | null = null
}

// ProductionStateModel Interface
export interface ProductionStateModel {
  settingsInfo: SettingsInfo
  exportFieldTemplates: ExportFieldTemplate[]
  exportFieldDetails: ExportFieldDetails[]
  savedSearches: SavedSearchItem[]
}
