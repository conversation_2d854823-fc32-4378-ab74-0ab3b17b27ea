import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  signal,
} from '@angular/core'
import { toSignal } from '@angular/core/rxjs-interop'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { DialogModule, DialogRef } from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { ProjectFacade } from '@venio/data-access/common'

@Component({
  selector: 'venio-media-processing-status-dialog',
  imports: [
    CommonModule,
    DialogModule,
    ButtonModule,
    GridModule,
    TooltipModule,
  ],
  templateUrl: './media-processing-status-dialog.component.html',
  styleUrl: './media-processing-status-dialog.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MediaProcessingStatusDialogComponent {
  public readonly dialogTitle = signal('Search')

  private readonly dialogRef = inject(DialogRef)

  private readonly projectFacade = inject(ProjectFacade)

  public readonly unIndexMediaStatus = toSignal(
    this.projectFacade.selectunIndexMediaSuccess$
  )

  public readonly isMediaStatusLoading = toSignal(
    this.projectFacade.selectIsMediaStatusLoading$,
    { initialValue: true }
  )

  /** Computed property for the media processed status */
  public readonly unIndexMediaList = computed(() => {
    const result = this.unIndexMediaStatus()
    return (result?.data || []).map((result, index) => ({
      ...result,
      sn: index + 1,
    }))
  })

  #navigateToDocuments(shouldRedirect: boolean): void {
    this.dialogRef.close({ shouldRedirect })
  }

  public proceed(): void {
    this.#navigateToDocuments(true)
  }

  public close(): void {
    this.#navigateToDocuments(false)
  }
}
