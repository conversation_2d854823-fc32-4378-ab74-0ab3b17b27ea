<div class="t-h-full t-flex t-flex-col">
  <div class="t-flex t-mb-1">
    <div class="t-flex-col t-w-1/2">
      <div class="t-mt-2">
        <span class="t-font-medium t-text-[#263238] t-text-[16px] t-mt-2 t-mb-1"
          >Redaction Option</span
        >
        <div class="t-flex t-py-2">
          <kendo-dropdownlist
            class="t-w-3/5"
            [data]="redactionOptions"
            [value]="redactionOption"
            (valueChange)="onRedactionOptionChange($event)"
            textField="label"
            valueField="value"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>
      </div>
      <kendo-grid
        [data]="redactions"
        [selectable]="true"
        [ngClass]="{
          'k-disabled t-opacity-50': redactionOption === 'all',
          't-opacity-100': redactionOption === 'selectedOnly'
        }"
        [height]="200"
        kendoGridSelectBy
        [resizable]="true"
        [selectable]="selectableSettings"
        [(selectedKeys)]="selectedRedaction"
        [kendoGridSelectBy]="getRedactionObjectId">
        <kendo-grid-checkbox-column
          [width]="45"
          [resizable]="false"
          [columnMenu]="false"
          [showSelectAll]="true"
          [resizable]="true"></kendo-grid-checkbox-column>
        <kendo-grid-column
          field="redactionSetName"
          title="Redaction Set Name"
          [width]="200"
          [resizable]="true"></kendo-grid-column>
        <kendo-grid-column field="reason" title="Reason"></kendo-grid-column>
        <kendo-grid-column
          field="redactionType"
          title="Type"
          [resizable]="true"></kendo-grid-column>
      </kendo-grid>
      <div class="t-flex t-flex-row t-justify-end t-ml-4">
        <button
          kendoButton
          class="t-mt-2 k-button-solid-secondary"
          *ngIf="!showBtnInGrid"
          (click)="checkQualifiedFiles()">
          Check Qualified Files
        </button>
      </div>
    </div>
    <div class="t-flex t-flex-row t-justify-end t-w-1/2 t-ml-4">
      <div [ngClass]="showBtnInGrid ? '!t-mt-1' : '!t-mt-5'">
        <div
          class="t-font-medium t-text-[#263238] t-text-[16px]"
          [ngClass]="showBtnInGrid ? '!t-mt-3' : '!t-mt-[38px]'">
          Summary
        </div>
        <div>
          <kendo-grid
            [kendoGridBinding]="gridData"
            filterable="menu"
            [hideHeader]="true"
            class="t-h-50 t-overflow-y-auto"
            data-qa="delete-document-summary-grid">
            <kendo-grid-column
              field="description"
              data-qa="create-slipsheet-summary-description">
              <ng-template kendoGridCellTemplate let-dataItem>
                <div class="k-whitespace-normal">
                  {{ dataItem.description }}
                </div>
              </ng-template>
            </kendo-grid-column>

            <kendo-grid-column
              [width]="80"
              field="count"
              class="!k-text-right !k-pr-2"
              data-qa="create-slipsheet-summary-count">
            </kendo-grid-column>

            <ng-template kendoGridNoRecordsTemplate>
              <div
                class="t-flex t-items-center k-actions-center no-data-height k-button-solid-secondary">
                <button
                  kendoButton
                  class="t-mt-2"
                  *ngIf="showBtnInGrid"
                  (click)="checkQualifiedFiles()">
                  Check Qualified Files
                </button>
              </div>
            </ng-template>
          </kendo-grid>
        </div>
      </div>
    </div>
  </div>

  <div *ngIf="fileTypeData?.length > 0">
    <div class="t-flex t-justify-center">
      <kendo-loader
        *ngIf="showSpinner"
        size="medium"
        type="pulsing"></kendo-loader>
    </div>
    <venio-treelist
      [data]="fileTypeData"
      parentIdField="fileTypeGroupID"
      idField="fileTypeID"
      selectedField="selected"
      *ngIf="!showSpinner"
      [sortable]="true"
      [height]="225"
      (venioTreeViewSelectionChange)="selectionChange($event)">
      <venio-treelist-column
        field="fileType"
        title="File Type"
        [sortable]="true"
        [width]="100"></venio-treelist-column>

      <venio-treelist-column
        field="fileTypeDescription"
        [sortable]="true"
        [width]="100"
        title="Description"></venio-treelist-column>

      <venio-treelist-column
        field="total"
        title="Count"
        [width]="200"
        [sortable]="true"
        class="!k-text-left !k-pr-4"></venio-treelist-column>
    </venio-treelist>
  </div>
</div>
