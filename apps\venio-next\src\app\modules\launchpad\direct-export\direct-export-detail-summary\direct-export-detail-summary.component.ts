import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  Input,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'

import { Subject, takeUntil } from 'rxjs'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import {
  BinderColorDescription,
  BinderSizeDescription,
  DataRetentionTypeDescription,
  BindingType,
  BindingTypeDescription,
  ConversionTypeDescription,
  CSVExcelHandlingDescription,
  DeduplicationOptionDescription,
  DocumentSeparatorDescription,
  EndorsementLocation,
  FamilyFileHandlingDescription,
  PaperSideDescription,
  PaperTypeDescription,
  PDFFamilyFileHandlingDescription,
  PDFFileNamingConventionDescription,
  PDFTypeDescription,
  PrefixDelimiterDescription,
  PrintSetOption,
  PrintSetOptionDescription,
  ServiceType,
  ServiceTypeConstants,
  SettingsInfo,
  DirectExportImageType,
  DirectExportImageTypeDescription,
} from '@venio/shared/models/interfaces'
import { DirectExportFacade } from '@venio/data-access/common'

@Component({
  selector: 'venio-direct-export-detail-summary',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './direct-export-detail-summary.component.html',
  styleUrl: './direct-export-detail-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DirectExportDetailSummaryComponent implements OnDestroy {
  private readonly directExportFacade = inject(DirectExportFacade)

  public selectedServiceTypeName = input<ServiceTypeConstants>()

  public selectedBaseAPIUrl = input<string>()

  public SERVICE_TYPE_STRING_CONSTANTS = ServiceTypeConstants

  public SERVICE_TYPE = ServiceType

  public BIND_TYPE = BindingType

  public PRINT_SET = PrintSetOption

  @Input() public settings!: SettingsInfo

  @Input() public settingsForm!: FormGroup

  @Input() public timeZones$: any[] = []

  @Input() public serviceTypeList: any[] = []

  public fieldTemplate = ''

  private unsubscribed$ = new Subject<void>()

  constructor() {}

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  public get fieldTemplateName(): string {
    const value =
      this.settingsForm['controls']?.productionSettings['controls']
        .fieldTemplateId?.value
    let templateName = ''
    this.directExportFacade.selectExportFieldTemplates$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((data: any) => {
        templateName = data.find((t) => t?.Id === value)?.Name
      })
    return templateName
  }

  public get serviceType(): string | undefined {
    return this.serviceTypeList.find(
      (item) =>
        item.serviceTypeId ===
        this.settingsForm['controls']?.ServiceRequestType?.value
    )?.serviceTypeDisplayName
  }

  public get printSetOption(): PrintSetOption {
    return this.settingsForm['controls']?.printServiceSettings['controls']
      .printSet?.value
  }

  public get clientMatterNumber(): string | null {
    return this.settingsForm['controls']?.clientMatterNumber?.value || null
  }

  public get caseName(): string {
    return this.settingsForm['controls']?.caseName?.value
  }

  public get exportTemplateName(): string {
    return this.settingsForm['controls']?.serviceType?.value?.exportTemplateName
  }

  public get createImages(): boolean {
    return this.settingsForm['controls']?.createImages?.value
  }

  public get dataRetentionType(): string {
    return DataRetentionTypeDescription.get(
      this.settingsForm['controls']?.dataRetentionType?.value
    )
  }

  public get timeZone(): string | undefined {
    if (this.timeZones$?.length > 0) {
      return this.timeZones$.find(
        (item) =>
          item.tzTimeZone ===
          this.settingsForm['controls']?.generalSettings['controls']?.timeZone
            ?.value
      )?.displayName
    }
    return undefined
  }

  public getServiceTypeName(): string {
    const serviceType = this.#getServiceRequestTypeName()

    switch (serviceType) {
      case this.SERVICE_TYPE_STRING_CONSTANTS.VODR_IMPORT_TO_RELATIVITY_SERVICE:
        return 'Relativity Service'
      case this.SERVICE_TYPE_STRING_CONSTANTS.VODR_PDF_SERVICE:
        return 'PDF Service'
      case this.SERVICE_TYPE_STRING_CONSTANTS.VODR_CONCORDANCE_SERVICE:
        return 'Concordance Service'
      case this.SERVICE_TYPE_STRING_CONSTANTS.VODR_SUMMATION_SERVICE:
        return 'Summation Service'
      case this.SERVICE_TYPE_STRING_CONSTANTS.VODR_PRINT_SERVICE:
        return 'Print Service'
      default:
        return ''
    }
  }

  public get deduplicationOption(): string {
    return DeduplicationOptionDescription.get(
      this.settingsForm['controls']?.generalSettings['controls']
        .deduplicationOption?.value
    )
  }

  public get csvHandling(): string {
    return CSVExcelHandlingDescription.get(
      this.settingsForm['controls']?.generalSettings['controls']
        ?.csvExcelHandling?.value
    )
  }

  public get discoveryExceptionHandling(): string {
    return this.settingsForm['controls']?.generalSettings['controls']
      .discoveryExceptionHandling?.value
      ? 'Notify me and allow file repair'
      : 'Do not notify me, complete the project and report'
  }

  public get passwords(): string {
    return this.settingsForm['controls']?.imageConversionSettings['controls']
      ?.passwordList?.value
  }

  public get sortOrder(): string {
    return this.prepareSortOrder()
  }

  public get startNumber(): number {
    return this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
      'controls'
    ].ControlNumberSetting['controls']?.controlNumberStartingNumber?.value
  }

  public get prefix(): string {
    return this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
      'controls'
    ].ControlNumberSetting['controls']?.controlNumberPrefix?.value
  }

  public get delimiter(): string {
    return PrefixDelimiterDescription.get(
      this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
        'controls'
      ].ControlNumberSetting['controls']?.controlNumberDelimiter?.value
    )
  }

  public get endorseControlNumber(): boolean {
    return this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
      'controls'
    ].ControlNumberSetting['controls']?.endorseControlNumber?.value
  }

  public get endorsementControlNumberLocation(): string {
    return EndorsementLocation[
      this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
        'controls'
      ].ControlNumberSetting['controls']?.controlNumberLocation?.value
    ]
  }

  public get endorseOptionalMessage(): boolean {
    return this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
      'controls'
    ].ControlNumberSetting['controls']?.endorseOptionalMessage?.value
  }

  public get endorseOptionalMessageText(): string {
    return this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
      'controls'
    ].ControlNumberSetting['controls']?.messageText?.value
  }

  public get endorsementOptionalMessageLocation(): string {
    return EndorsementLocation[
      this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
        'controls'
      ].ControlNumberSetting['controls']?.messageTextLocation?.value
    ]
  }

  public get volumeId(): string {
    return this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
      'controls'
    ].ControlNumberSetting['controls']?.volumeId?.value
  }

  private prepareSortOrder(): string {
    switch (
      this.settingsForm['controls']?.controlNumberAndEndorsementSettings[
        'controls'
      ].sortOrder?.value
    ) {
      case 'RELATIVE_FILE_PATH':
        return 'Original Discovery Order'
      case 'GROUP_DATE_ASC':
        return 'Sort By Date – Oldest to Newest'
      case 'GROUP_DATE_DESC':
        return 'Sort By Date – Newest to Oldest'
      default:
        return ''
    }
  }

  public get isConcordanceOrSummationService(): boolean {
    const serviceType = this.#getServiceRequestTypeName()
    return (
      serviceType === 'VODR_CONCORDANCE_SERVICE' ||
      serviceType === 'VODR_SUMMATION_SERVICE'
    )
  }

  public get directExportImageType(): string {
    return DirectExportImageTypeDescription.get(
      this.settingsForm['controls']?.imageConversionSettings['controls']
        ?.imageType?.value
    )
  }

  public get showImageConversionSettings(): boolean {
    return (
      !this.isConcordanceOrSummationService ||
      (this.isConcordanceOrSummationService &&
        this.settingsForm['controls']?.imageConversionSettings['controls']
          ?.imageType?.value !== DirectExportImageType.NONE)
    )
  }

  public get imageColorConversion(): string {
    return ConversionTypeDescription.get(
      this.settingsForm['controls']?.imageConversionSettings['controls']
        .imageColorConversion['controls']?.imageFileType?.value
    )
  }

  public get pdfColorConversion(): string {
    return ConversionTypeDescription.get(
      this.settingsForm['controls']?.imageConversionSettings['controls']
        .imageColorConversion['controls']?.pdfFiles?.value
    )
  }

  public get powerpointColorConversion(): string {
    return ConversionTypeDescription.get(
      this.settingsForm['controls']?.imageConversionSettings['controls']
        .imageColorConversion['controls']?.powerpoint?.value
    )
  }

  public get excludeAlreadyProducedDocuments(): boolean {
    return this.settingsForm['controls']?.productionSettings['controls']
      .excludeProducedDocuments?.value
  }

  public get excludeNativeForRedactedDocuments(): boolean {
    return this.settingsForm['controls']?.productionSettings['controls']
      .excludeNativeForRedactedDocuments?.value
  }

  public get relativityFieldMappingTemplateName(): number {
    return this.settingsForm['controls']?.productionSettings['controls']
      .relativityFieldMappingTemplateName?.value
  }

  public get connectorName(): string {
    return this.settingsForm['controls']?.productionSettings['controls']
      .connector['controls'].name?.value
  }

  public get workspaceName(): string {
    return this.settingsForm['controls']?.productionSettings['controls']
      .connector['controls'].workspaceName?.value
  }

  public get connectorFileSharePath(): string {
    return this.settingsForm['controls']?.productionSettings['controls']
      .connector['controls'].connectorFileSharePath?.value
  }

  public get isRelativityOne(): boolean {
    return (
      this.settingsForm['controls']?.productionSettings['controls']?.connector[
        'controls'
      ]?.connectorPlatform?.value?.toLowerCase() === 'relativityone'
    )
  }

  public get pdfType(): string {
    return PDFTypeDescription.get(
      this.settingsForm['controls']?.pdfServiceSettings['controls']?.pdfType
        ?.value
    )
  }

  public get pdfFamilyFileHandling(): string {
    return PDFFamilyFileHandlingDescription.get(
      this.settingsForm['controls']?.pdfServiceSettings['controls']
        .pdfFamilyFileHandling?.value
    )
  }

  public get pdfFileNaming(): string {
    return PDFFileNamingConventionDescription.get(
      this.settingsForm['controls']?.pdfServiceSettings['controls']
        .pdfFileNamingConvention?.value
    )
  }

  public get paperType(): string {
    return PaperTypeDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']?.paperType
        ?.value
    )
  }

  public get paperSide(): string {
    return PaperSideDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']?.paperSide
        ?.value
    )
  }

  public get documentSeparator(): string {
    return DocumentSeparatorDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']
        .documentSeparator?.value
    )
  }

  public get printFamilyFileHandling(): string {
    return FamilyFileHandlingDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']
        .printFamilyFileHandling?.value
    )
  }

  public get bindingTypeOption(): BindingType {
    return this.settingsForm['controls'].printServiceSettings['controls']
      .binding.value
  }

  public get bindingType(): string {
    return BindingTypeDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']?.binding
        ?.value
    )
  }

  public get binderSize(): string {
    return BinderSizeDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']
        .threeRingBinderSize?.value
    )
  }

  public get binderColor(): string {
    return BinderColorDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']
        .threeRingBinderColor?.value
    )
  }

  public get printSet(): string {
    return PrintSetOptionDescription.get(
      this.settingsForm['controls']?.printServiceSettings['controls']?.printSet
        ?.value
    )
  }

  public get printSetNumber(): number {
    return this.settingsForm['controls']?.printServiceSettings['controls']
      .numberOfSets?.value
  }

  public get isPdfService(): boolean {
    return false
  }

  public get isPrintService(): boolean {
    return false
  }

  #getServiceRequestTypeName(): string | undefined {
    let serviceTypeId = this.settingsForm.controls.ServiceRequestType?.value
    if (serviceTypeId === ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT) {
      serviceTypeId =
        this.settingsForm.controls.serviceRequestTypeExisting.value
    }

    return this.serviceTypeList && this.serviceTypeList?.length > 0
      ? (this.serviceTypeList?.find(
          (val) => val?.serviceTypeId === serviceTypeId
        )?.serviceTypeName as string | undefined)
      : undefined
  }
}
