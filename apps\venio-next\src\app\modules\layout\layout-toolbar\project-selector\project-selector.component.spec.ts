import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProjectSelectorComponent } from './project-selector.component'
import { ActivatedRoute } from '@angular/router'
import {
  CaseInfoFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { PLATFORM_ID } from '@angular/core'
import {
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { ProjectFacade, UserFacade } from '@venio/data-access/common'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { of } from 'rxjs'

describe('ProjectSelectorComponent', () => {
  let component: ProjectSelectorComponent
  let fixture: ComponentFixture<ProjectSelectorComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ProjectSelectorComponent,
        NoopAnimationsModule,
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
        IframeMessengerModule.forRoot({}),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        CaseInfoFacade,
        SearchFacade,
        FieldFacade,
        UserFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
        {
          provide: ProjectFacade,
          useValue: {
            selectunIndexMediaSuccess$: of({
              data: [
                { custodianName: 'File A', mediaName: '', mediaStatus: '' },
                { custodianName: 'File B', mediaName: '', mediaStatus: '' },
              ],
            }),
            selectIsMediaStatusLoading$: of(false),
            fetchUnIndexMediaStatus: jest.fn(),
            fetchSearchOptions: jest.fn(),
          },
        },
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ProjectSelectorComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should call onProjectChange when the user selects a different case from the dropdown', () => {
    // GIVEN the user selects a new case (project) from the dropdown
    const newProjectId = 2

    // AND a spy is set on the onProjectChange method
    const onProjectChangeSpy = jest.spyOn(component, 'onProjectChange')

    // WHEN the valueChange event is triggered with a new projectId
    component.onProjectChange(newProjectId)

    // THEN the onProjectChange method should be called with the selected projectId
    expect(onProjectChangeSpy).toHaveBeenCalledWith(newProjectId)
  })

  it('should fetch unindexed media status when the user selects a new case from the dropdown', () => {
    // GIVEN a new projectId selected from the dropdown
    const newProjectId = 2

    // AND a spy on the onProjectChange and fetchUnIndexMediaStatus methods to track calls
    const onProjectChangeSpy = jest.spyOn(component, 'onProjectChange')
    const fetchUnIndexMediaStatusSpy = jest.spyOn(
      component,
      'fetchUnIndexMediaStatus'
    )

    // WHEN the valueChange event is triggered by selecting a new projectId
    component.onProjectChange(newProjectId)

    // THEN onProjectChange should be called with the new projectId
    expect(onProjectChangeSpy).toHaveBeenCalledWith(newProjectId)

    // AND THEN it should call fetchUnIndexMediaStatus with the correct projectId
    expect(fetchUnIndexMediaStatusSpy).toHaveBeenCalledWith(newProjectId)
  })
})
