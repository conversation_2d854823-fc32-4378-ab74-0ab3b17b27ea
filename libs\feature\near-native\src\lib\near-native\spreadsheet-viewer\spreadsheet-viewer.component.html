<div class="v-warning-alert mb-0" *ngIf="errorMessage">
  <div class="d-flex justify-content-between">
    <div class="mr-3">{{ errorMessage }}</div>
  </div>
</div>
<div class="t-flex t-flex-row t-gap-2 t-h-full" [hidden]="errorMessage">
  <div *venioHasUserGroupRights="UserRights.ALLOW_TO_APPLY_NATIVE_REDACTION">
    <div class="t-flex t-flex-col t-gap-2 t-mt-[2.2rem]" *ngIf="showToolbars">
      <button
        kendoButton
        #redact
        class="!t-p-[0.3rem]"
        (click)="actionButtonHandler(SpreadsheetAction.REDACT)"
        fillMode="clear"
        title="Redaction"
        size="none">
        <span
          [parentElement]="redact.element"
          venioSvgLoader
          hoverColor="#FFBB12"
          [svgUrl]="'assets/svg/icon-pdf-utilities-hand-stretch.svg'"
          height="1.3rem"
          width="1.3rem"></span>
      </button>

      <button
        kendoButton
        #highlight
        class="!t-p-[0.3rem]"
        (click)="actionButtonHandler(SpreadsheetAction.HIGHLIGHT)"
        fillMode="clear"
        title="Highlight"
        size="none">
        <span
          [parentElement]="highlight.element"
          venioSvgLoader
          hoverColor="#FFBB12"
          [svgUrl]="'assets/svg/icon-pdf-utilities-pen-fancy.svg'"
          height="1.3rem"
          width="1.3rem"></span>
      </button>

      <button
        kendoButton
        #unredact
        class="!t-p-[0.3rem]"
        (click)="actionButtonHandler(SpreadsheetAction.UNREDACT)"
        fillMode="clear"
        title="Remove Redaction"
        size="none">
        <span
          [parentElement]="unredact.element"
          venioSvgLoader
          hoverColor="#FFBB12"
          [svgUrl]="'assets/svg/icon-pdf-utilities-red-cross.svg'"
          height="1.3rem"
          width="1.3rem"></span>
      </button>
    </div>
  </div>
  <div class="t-flex t-flex-col t-w-full t-gap-2">
    <div
      class="t-flex t-gap-2"
      *venioHasUserGroupRights="UserRights.ALLOW_TO_APPLY_NATIVE_REDACTION">
      <button
        kendoButton
        #redactionMode
        class="!t-p-[0.3rem]"
        (click)="actionButtonHandler(SpreadsheetAction.ENABLE_REDACTION)"
        fillMode="clear"
        title="Redaction Mode"
        size="none">
        <span
          [parentElement]="redactionMode.element"
          venioSvgLoader
          hoverColor="#FFBB12"
          [svgUrl]="'assets/svg/icon-pdf-utilities-redaction.svg'"
          height="1.3rem"
          width="1.3rem"></span>
      </button>

      <button
        kendoButton
        #save
        class="!t-p-[0.3rem]"
        (click)="actionButtonHandler(SpreadsheetAction.SAVE)"
        fillMode="clear"
        title="Save"
        size="none">
        <span
          [parentElement]="save.element"
          venioSvgLoader
          hoverColor="#FFBB12"
          [svgUrl]="'assets/svg/Icon-material-save.svg'"
          height="1.3rem"
          width="1.3rem"></span>
      </button>

      <button
        kendoButton
        #save
        class="!t-p-[0.3rem]"
        [disabled]="
          showToolbars || !originalSpreadSheetContent() || !hasAnnotation
        "
        (click)="ShowHideAnnotation()"
        fillMode="clear"
        [title]="annotationsHidden ? 'Show Annotations' : 'Hide Annotations'"
        size="none">
        <span
          [parentElement]="save.element"
          venioSvgLoader
          hoverColor="#FFBB12"
          [svgUrl]="
            annotationsHidden ? 'assets/svg/Show.svg' : 'assets/svg/Hide.svg'
          "
          height="1.3rem"
          width="1.3rem"></span>
      </button>
    </div>
    <ejs-spreadsheet
      #spreadsheet
      id="spreadsheet"
      [enableContextMenu]="false"
      [allowEditing]="true"
      [allowFiltering]="true"
      (select)="selectCell($event)"
      [showRibbon]="false">
      <e-sheets>
        <e-sheet [isProtected]="true"></e-sheet>
      </e-sheets>
    </ejs-spreadsheet>
  </div>
</div>

<kendo-loader *ngIf="showSpinner" size="medium" type="pulsing"></kendo-loader>
