import { Injectable } from '@angular/core'
import { FormGroup, FormBuilder, FormControl } from '@angular/forms'
import {
  ImageDimensionSettings,
  SystemBatesModel,
} from '@venio/data-access/review'

@Injectable({ providedIn: 'root' })
export class ConverToImageFormService {
  public imageForm!: FormGroup

  private initialValue: {
    systemBates: SystemBatesModel
    pageLimitOption: string
    customMaxpageLimit: number
    imageDimensionSettings: ImageDimensionSettings
  }

  constructor(private fb: FormBuilder) {
    this.initializeForm()
  }

  /**
   * Returns the print form
   * @returns {void}
   */
  public resetForm(): void {
    this.imageForm.reset(this.initialValue)
  }

  /**
   * Initializes the print form
   * @returns {void}
   */
  private initializeForm(): void {
    this.imageForm = this.fb.group({
      systemBates: this.fb.group({
        prefixText: [{ value: 'IMG', disabled: true }],
        prefixField: new FormControl({
          value: 'DOCUMENT_UNIQUE_IDENTIFIER',
          disabled: true,
        }),
        padding: [{ value: 8, disabled: true }],
        startingNum: [{ value: 1, disabled: true }],
        generateBates: false,
        brandingBates: new FormControl({ value: false, disabled: true }),
        autogenerateTiff: false,
        ignoreAutoTiffJobsForMediaProcessingStatus: false,
        prefixType: new FormControl({
          value: 'TEXT',
          disabled: true,
        }),
      }),
      pageLimitOption: 'PROJECT_PAGE_LIMIT', //'CUSTOM_PAGE_LIMIT'
      customMaxpageLimit: [{ value: 1000, disabled: true }],
      imageDimensionSettings: this.fb.group({
        imageDimension: 'DEFAULT',
        customWidth: [{ value: 2250, disabled: true }],
        customHeight: [{ value: 3000, disabled: true }],
        dpi: [{ value: 300, disabled: true }],
      }),
    })

    this.initialValue = this.imageForm.getRawValue() as {
      systemBates: SystemBatesModel
      pageLimitOption: string
      customMaxpageLimit: number
      imageDimensionSettings: ImageDimensionSettings
    }
  }
}
