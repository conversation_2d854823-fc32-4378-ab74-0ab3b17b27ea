import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MediaProcessingStatusDialogComponent } from './media-processing-status-dialog.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideMockStore } from '@ngrx/store/testing'
import { ProjectFacade } from '@venio/data-access/common'
import { of } from 'rxjs'

describe('MediaProcessingStatusDialogComponent', () => {
  let component: MediaProcessingStatusDialogComponent
  let fixture: ComponentFixture<MediaProcessingStatusDialogComponent>
  let dialogRefMock: any

  beforeEach(async () => {
    dialogRefMock = {
      close: jest.fn(),
    }
    await TestBed.configureTestingModule({
      imports: [MediaProcessingStatusDialogComponent],
      providers: [
        {
          provide: ProjectFacade,
          useValue: {
            selectunIndexMediaSuccess$: of({
              data: [
                { custodianName: 'File A', mediaName: '', mediaStatus: '' },
                { custodianName: 'File B', mediaName: '', mediaStatus: '' },
              ],
            }),
            selectIsMediaStatusLoading$: of(false),
          },
        },
        { provide: DialogRef, useValue: dialogRefMock },
        provideMockStore({}),
        provideHttpClient(),
      ],
    }).compileComponents()

    // Create the component
    fixture = TestBed.createComponent(MediaProcessingStatusDialogComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  it('should initialize the dialog title with the default value "Search"', () => {
    // GIVEN the component is initialized

    // WHEN the dialogTitle signal value is accessed
    const title = component.dialogTitle()

    // THEN the dialog title should be initialized to "Search"
    expect(title).toBe('Search')
  })

  it('should indicate that media status loading is complete by default', () => {
    // GIVEN the component is initialized and the media status loading observable emits false

    // WHEN the isMediaStatusLoading signal value is accessed
    const isLoading = component.isMediaStatusLoading()

    // THEN the loading state should be false, indicating that loading is complete
    expect(isLoading).toBe(false)
  })

  it('should close the dialog and trigger redirection when the user confirms the action', () => {
    // GIVEN the dialog is open and the user chooses to proceed with the operation

    // WHEN the user clicks the "Proceed" button
    component.proceed()

    // THEN the dialog should close and trigger navigation with shouldRedirect set to true
    expect(dialogRefMock.close).toHaveBeenCalledWith({ shouldRedirect: true })
  })

  it('should close the dialog without redirection when the user cancels the action', () => {
    // GIVEN the dialog is open and the user chooses to cancel the operation

    // WHEN the user clicks the "Cancel" button
    component.close()

    // THEN the dialog should close and no redirection should occur (shouldRedirect is false)
    expect(dialogRefMock.close).toHaveBeenCalledWith({ shouldRedirect: false })
  })

  it('should correctly compute and assign serial numbers to each item in the unindexed media list', () => {
    // GIVEN the component receives a list of unindexed media from the ProjectFacade

    // WHEN the unIndexMediaList computed property is accessed
    const mediaList = component.unIndexMediaList()

    // THEN each media item should have a serial number assigned correctly starting from 1
    expect(mediaList).toEqual([
      { custodianName: 'File A', mediaName: '', mediaStatus: '', sn: 1 },
      { custodianName: 'File B', mediaName: '', mediaStatus: '', sn: 2 },
    ])
  })
})
