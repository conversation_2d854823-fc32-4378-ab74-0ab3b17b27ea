import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { SpreadsheetViewerComponent } from './spreadsheet-viewer.component'
import {
  VenioNotificationModule,
  VenioNotificationService,
} from '@venio/feature/notification'
import { NearNativeFacade } from '../../services/near-native.facade'
import { provideMockStore } from '@ngrx/store/testing'
import { ReviewPanelFacade } from '@venio/data-access/document-utility'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { IndexedDBHandlerService } from '@venio/data-access/review'

describe('SpreadsheetViewerComponent', () => {
  let component: SpreadsheetViewerComponent
  let fixture: ComponentFixture<SpreadsheetViewerComponent>
  let spreadsheetMock: any

  beforeEach(async () => {
    const mockNearNativeFacade = {
      fetchExcelAnnotation: jest.fn(),
      fetchExcelWorkBook: jest.fn(),
      saveExcelAnnotation: jest.fn(),
    }

    const mockIndexDb = {
      ifSpecificPartExistsInSpreadSheet: jest.fn(),
      getSpreadsheetParts: jest.fn(),
      addSpreadsheetParts: jest.fn(),
    }

    const mockSpreadsheet = {
      openFromJson: jest.fn(),
      cellFormat: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [SpreadsheetViewerComponent, VenioNotificationModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NearNativeFacade, useValue: mockNearNativeFacade },
        { provide: IndexedDBHandlerService, useValue: mockIndexDb },
        ReviewPanelFacade,
        VenioNotificationService,
        provideMockStore(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(SpreadsheetViewerComponent)
    component = fixture.componentInstance
    nearNativeFacadeMock = TestBed.inject(NearNativeFacade) as any
    indexDbMock = TestBed.inject(IndexedDBHandlerService) as any

    // Set up the spreadsheet mock and cast to any to avoid TypeScript issues
    spreadsheetMock = mockSpreadsheet
    component.spreadsheetObj = spreadsheetMock as any

    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('ShowHideAnnotation', () => {
    beforeEach(() => {
      // Set up initial data for tests
      component['originalSpreadSheetContent'].set(
        JSON.stringify({
          Workbook: { sheets: [] },
        })
      )

      // Ensure spreadsheet object is properly set up
      component.spreadsheetObj = spreadsheetMock as any

      // Spy on onOpenComplete method with Jest
      jest.spyOn(component, 'onOpenComplete').mockImplementation(() => {})
    })

    it('should do nothing if there is no spreadsheet content', () => {
      // Reset the content to empty
      component['originalSpreadSheetContent'].set('')

      // Call the method
      component.ShowHideAnnotation()

      // Verify spreadsheet was not opened with new JSON
      expect(spreadsheetMock.openFromJson).not.toHaveBeenCalled()
    })

    it('should toggle annotations hidden state from false to true', () => {
      // Initial state is false (annotations visible)
      expect(component.annotationsHidden).toBeFalsy()

      // Call the method
      component.ShowHideAnnotation()

      // Verify state was toggled
      expect(component.annotationsHidden).toBeTruthy()

      // Verify spreadsheet was opened with new JSON
      expect(spreadsheetMock.openFromJson).toHaveBeenCalled()

      // Verify onOpenComplete was not called (because annotations are now hidden)
      expect(component.onOpenComplete).not.toHaveBeenCalled()
    })

    it('should toggle annotations hidden state from true to false', fakeAsync(() => {
      // Set initial state to true (annotations hidden)
      component['isAnnotationHidden'].set(true)

      // Call the method
      component.ShowHideAnnotation()

      // Verify state was toggled
      expect(component.annotationsHidden).toBeFalsy()

      // Verify spreadsheet was opened with new JSON
      expect(spreadsheetMock.openFromJson).toHaveBeenCalled()

      // Advance the timer to simulate setTimeout
      tick(100)

      // Verify onOpenComplete was called (because annotations are now visible)
      expect(component.onOpenComplete).toHaveBeenCalledWith(null)
    }))

    it('should parse and use the original spreadsheet content', () => {
      const testContent = JSON.stringify({
        Workbook: {
          sheets: [{ name: 'Sheet1' }],
          activeSheetIndex: 5, // This should be reset to 0
        },
      })

      // Set up test content
      component['originalSpreadSheetContent'].set(testContent)

      // Call the method
      component.ShowHideAnnotation()

      // Verify the JSON was parsed and opened with activeSheetIndex reset to 0
      expect(spreadsheetMock.openFromJson).toHaveBeenCalledWith(
        expect.objectContaining({
          file: expect.objectContaining({
            Workbook: expect.objectContaining({
              activeSheetIndex: 0,
            }),
          }),
          triggerEvent: true,
        })
      )
    })

    it('should handle content without Workbook property', () => {
      const testContent = JSON.stringify({
        sheets: [{ name: 'Sheet1' }],
      })

      // Set up test content without Workbook
      component['originalSpreadSheetContent'].set(testContent)

      // Call the method
      component.ShowHideAnnotation()

      // Verify the JSON was parsed and opened as-is
      expect(spreadsheetMock.openFromJson).toHaveBeenCalledWith(
        expect.objectContaining({
          file: expect.objectContaining({
            sheets: [{ name: 'Sheet1' }],
          }),
          triggerEvent: true,
        })
      )
    })
  })
})
