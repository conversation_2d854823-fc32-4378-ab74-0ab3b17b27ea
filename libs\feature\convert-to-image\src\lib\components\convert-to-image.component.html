<div class="t-h-full t-flex t-flex-col">
  <div class="t-font-medium t-text-[#263238] t-text-[16px] t-mt-2 t-mb-1">
    Summary
  </div>
  <div class="t-mb-2">
    <kendo-grid
      data-qa="convert-to-image-summary-grid"
      [kendoGridBinding]="summaryData"
      filterable="menu"
      [hideHeader]="true"
      class="t-h-50 t-overflow-y-auto"
      data-qa="delete-document-summary-grid">
      <kendo-grid-column
        [width]="400"
        field="titleKey"
        data-qa="delete-document-summary-description">
      </kendo-grid-column>
      <kendo-grid-column
        field="countValue"
        data-qa="delete-document-summary-count">
      </kendo-grid-column>
    </kendo-grid>
  </div>
  <div class="t-mt-2 t-mb-4 t-flex t-align-center t-justify-center">
    <kendo-expansionpanel
      id="advanced-option"
      data-qa="convert-to-image-advanced-option"
      [(expanded)]="advancedOptionExpansionState"
      class="v-custom-expansion-panel t-pb-1"
      subtitle="Advanced Option">
      <ng-container
        *ngComponentOutlet="printImageSourceComponent | async"></ng-container>
    </kendo-expansionpanel>
  </div>
  <div (resize)="onContainerResize($event)" class="t-h-full" #treelistContainer>
    <div class="t-flex t-justify-center">
      <kendo-loader
        *ngIf="showSpinner"
        size="medium"
        type="pulsing"></kendo-loader>
    </div>
    <div>
      <venio-treelist
        [data]="fileTypeData"
        data-qa="convert-to-image-file-type-tree-list"
        parentIdField="null"
        idField="fileTypeGroupId"
        selectedField="selected"
        [sortable]="true"
        [height]="fileTypeGridHeight"
        [resizable]="true"
        (venioTreeViewSelectionChange)="selectionChange($event)"
        class="v-custom-grid-table"
        *ngIf="!showSpinner">
        <venio-treelist-column
          data-qa="convert-to-image-file-type-group-column"
          field="fileTypeGroup"
          title="File Type Group"
          [sortable]="true"
          [width]="200"
          [resizable]="true"></venio-treelist-column>

        <venio-treelist-column
          data-qa="convert-to-image-count-column"
          field="cnt"
          title="Count"
          [sortable]="true"
          [width]="90"
          class="!k-text-left !k-pr-8 t-text-primary"
          [resizable]="true"></venio-treelist-column>

        <venio-treelist-column
          field="myPageLimit"
          title="Page Limit"
          [width]="150"
          [sortable]="false"
          [resizable]="true">
          <ng-template let-dataItem>
            <kendo-dropdownlist
              data-qa="convert-to-image-page-limit-dropdown-column"
              [data]="dataItem['pageLimitdb']"
              [(ngModel)]="dataItem['myPageLimit']"
              (click)="dropdownClick($event)"
              (valueChange)="dropdownValueChange(dataItem)">
            </kendo-dropdownlist>
          </ng-template>
        </venio-treelist-column>

        <venio-treelist-column
          field="imagingEngine"
          title="Engine"
          [sortable]="false"
          [width]="180"
          [resizable]="true">
          <ng-template let-dataItem>
            <kendo-dropdownlist
              data-qa="convert-to-image-page-engine-column"
              class="t-w-full"
              [data]="dataItem['engineDb']"
              [(ngModel)]="dataItem['imagingEngine']"
              (click)="dropdownClick($event)"
              (valueChange)="dropdownValueChange(dataItem)">
            </kendo-dropdownlist>
          </ng-template>
        </venio-treelist-column>

        <venio-treelist-column
          field="colorConversion"
          title="Color Conversion"
          [sortable]="false"
          [resizable]="true">
          <ng-template let-dataItem>
            <kendo-dropdownlist
              class="t-w-1/2"
              data-qa="convert-to-image-page-limit-color-column"
              [data]="dataItem['colorDb']"
              [(ngModel)]="dataItem['colorConversion']"
              (click)="dropdownClick($event)"
              (valueChange)="dropdownValueChange(dataItem)">
            </kendo-dropdownlist>
          </ng-template>
        </venio-treelist-column>
      </venio-treelist>
    </div>
  </div>
  <div class="t-mt-2 t-mb-4 t-flex t-align-center t-justify-center">
    <kendo-expansionpanel
      class="v-custom-expansion-panel"
      id="v-details-panel"
      [(expanded)]="detailsPanelExpansionState"
      subtitle="Details">
      <ng-container
        *ngComponentOutlet="jobStatusComponent | async"></ng-container>
    </kendo-expansionpanel>
  </div>
</div>
