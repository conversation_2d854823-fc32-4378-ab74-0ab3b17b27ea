import { Action, createReducer, on } from '@ngrx/store'
import {
  CaseDetailModel,
  CaseDetailResponseModel,
  CaseDetailRequestInfo,
  ResponseModel,
  ReviewSetSummary,
  ReviewSetDetailRequestModel,
  ReviewSetEntry,
  ReviewSetBatchRequestModel,
  ReviewSetBatchModel,
  UserGroupTree,
} from '@venio/shared/models/interfaces'
import * as ProjectActions from './project.actions'
import { resetStateProperty } from '@venio/util/utilities'
import { CaseModel, UserRights } from '@venio/data-access/review'

export const PROJECT_FEATURE_KEY = 'projectStore'

export interface ProjectState {
  // Loading State Indicators: Flags to indicate if a certain operation is currently in progress
  isProjectGroupLoading: boolean | undefined
  isProjectsLoading: boolean | undefined

  // Project Group Operation Responses: Success and error responses for project group operations
  projectGroupSuccessResponse: ResponseModel | undefined
  projectGroupErrorResponse: ResponseModel | undefined

  // Project Folder Tree Operation Responses: Success and error responses for project folder tree operations
  projectFolderTreeWithCountSuccessResponse: ResponseModel | undefined
  projectFolderTreeWithCountErrorResponse: ResponseModel | undefined

  // Project Search Option Operation Responses: Success and error responses for project search option operations
  searchOptionSuccessResponse: number | undefined
  searchOptionErrorResponse: ResponseModel | undefined

  // Projects Operation Responses: Success and error responses for project operations
  fetchProjectSuccessResponse: CaseModel[] | undefined
  fetchProjectErrorResponse: ResponseModel | undefined

  // Project Info Operation Responses: Success and error responses for project info operations
  projectInfoSuccessResponse: ResponseModel | undefined
  projectInfoErrorResponse: ResponseModel | undefined

  // Case Detail Operation Responses: Success and error responses for case detail operations
  isCaseDetailLoading: boolean | undefined
  caseDetailSuccessResponse: CaseDetailResponseModel | undefined
  caseDetailErrorResponse: ResponseModel | undefined
  caseDetailRequestInfo: CaseDetailRequestInfo
  selectedCaseDetail: CaseDetailModel[] | undefined

  // Toggling Favorite Project Operation Responses: Success and error responses for toggling favorite project operations
  isFavoriteProjectToggleLoading: Record<number, boolean> | undefined
  hasAnyFavoriteProject: boolean | undefined

  // Project Rights Operation Responses: Success and error responses for project rights operations
  isProjectRightLoading: boolean | undefined
  rightsToProjectIds: Record<UserRights, number[]> | undefined
  projectIdsToRights: Record<number, UserRights[]> | undefined
  projectRightErrorResponse: ResponseModel | undefined

  // Review Set Summary Detail Operation Responses: Success and error responses for review set summary detail operations
  isReviewSetSummaryDetailLoading: boolean | undefined
  reviewSetSummaryDetailSuccess: ReviewSetSummary | undefined
  reviewSetSummaryDetailError: ResponseModel | undefined
  reviewSetSummaryDetailRequestInfo: ReviewSetDetailRequestModel
  selectedReviewSetSummaryDetail: ReviewSetEntry[] | undefined

  // Review Set Delete Operation Responses: Success and error responses for review set delete operations
  isReviewSetDeleteLoading: string | undefined
  reviewSetDeleteSuccess: ResponseModel | undefined
  reviewSetDeleteError: ResponseModel | undefined

  // Tagged Document Tags Operation Responses: Success and error responses for tagged document tags operations
  isTaggedDocumentTagsLoading: boolean | undefined
  taggedDocumentTagsSuccess: ResponseModel | undefined
  taggedDocumentTagsError: ResponseModel | undefined

  // Review Set Document View Operation Responses: Success and error responses for review set document view operations
  isReviewSetDocumentViewLoading: boolean | undefined
  reviewSetDocumentViewSuccess: ResponseModel | undefined
  reviewSetDocumentViewError: ResponseModel | undefined

  // Review Set Batch Operation Responses: Success and error responses for review set batch operations
  isReviewSetBatchLoading: boolean | undefined
  reviewSetBatchSuccess: ReviewSetBatchModel[] | undefined
  reviewSetBatchError: ResponseModel | undefined
  reviewSetBatchRequestInfo: ReviewSetBatchRequestModel
  selectedBatchDetail: ReviewSetBatchModel[] | undefined

  // Review Set Reassign Operation Responses: Success and error responses for review set batch operations
  reviewSetReassignSuccess: ResponseModel | undefined
  reviewSetReassignError: ResponseModel | undefined

  // Review Set Rebatch Operation Responses: Success and error responses for review set batch operations
  reviewSetRebatchSuccess: ResponseModel | undefined
  reviewSetReBatchError: ResponseModel | undefined

  // Review Set delete batch Operation Responses: Success and error responses for review set batch operations
  reviewSetDeleteBatchSuccess: ResponseModel | undefined
  reviewSetDeleteBatchError: ResponseModel | undefined

  // Review set fetch user group
  isReviewSetUserGroupLoading: boolean | undefined
  reviewSetUserGroupSuccess: UserGroupTree[] | undefined
  reviewSetUserGroupError: ResponseModel | undefined

  // Upload invitation access operation responses
  uploadInvitationAccessResponse: ResponseModel | undefined
  uploadInvitationAccessErrorResponse: ResponseModel | undefined

  isMediaStatusLoading: boolean | undefined
  unIndexMediaSuccessResponse: ResponseModel | undefined
  unIndexMediaErrorResponse: ResponseModel | undefined
}

export const projectInitialState: ProjectState = {
  projectGroupErrorResponse: undefined,
  projectGroupSuccessResponse: undefined,
  isProjectGroupLoading: undefined,
  isProjectsLoading: undefined,
  projectFolderTreeWithCountErrorResponse: undefined,
  projectFolderTreeWithCountSuccessResponse: undefined,
  searchOptionErrorResponse: undefined,
  searchOptionSuccessResponse: undefined,
  fetchProjectErrorResponse: undefined,
  fetchProjectSuccessResponse: undefined,
  projectInfoErrorResponse: undefined,
  projectInfoSuccessResponse: undefined,
  isCaseDetailLoading: undefined,
  isFavoriteProjectToggleLoading: undefined,
  isProjectRightLoading: undefined,
  caseDetailErrorResponse: undefined,
  caseDetailSuccessResponse: undefined,
  selectedCaseDetail: undefined,
  hasAnyFavoriteProject: undefined,
  rightsToProjectIds: undefined,
  projectIdsToRights: undefined,
  projectRightErrorResponse: undefined,
  caseDetailRequestInfo: {
    pageNumber: 1,
    pageSize: 25,
    sortField: 'ProjectId',
    sortOrder: 'desc',
    // Initially, it should  pull everything
    caseTypeFilter: -1,
  },

  isReviewSetSummaryDetailLoading: undefined,
  reviewSetSummaryDetailError: undefined,
  reviewSetSummaryDetailSuccess: undefined,
  selectedReviewSetSummaryDetail: undefined,
  reviewSetSummaryDetailRequestInfo: {
    pageNumber: 1,
    pageSize: 25,
    sortField: 'CreatedOn',
    sortOrder: 'desc',
  },

  isReviewSetDeleteLoading: undefined,
  reviewSetDeleteError: undefined,
  reviewSetDeleteSuccess: undefined,

  isTaggedDocumentTagsLoading: undefined,
  taggedDocumentTagsSuccess: undefined,
  taggedDocumentTagsError: undefined,

  isReviewSetDocumentViewLoading: undefined,
  reviewSetDocumentViewSuccess: undefined,
  reviewSetDocumentViewError: undefined,
  isReviewSetBatchLoading: undefined,
  reviewSetBatchSuccess: undefined,
  reviewSetBatchError: undefined,
  reviewSetBatchRequestInfo: {
    pageNumber: 1,
    pageSize: 15,
  },
  selectedBatchDetail: undefined,
  isReviewSetUserGroupLoading: undefined,
  reviewSetUserGroupSuccess: undefined,
  reviewSetUserGroupError: undefined,
  reviewSetReassignSuccess: undefined,
  reviewSetReassignError: undefined,
  reviewSetRebatchSuccess: undefined,
  reviewSetReBatchError: undefined,
  reviewSetDeleteBatchSuccess: undefined,
  reviewSetDeleteBatchError: undefined,

  uploadInvitationAccessResponse: undefined,
  uploadInvitationAccessErrorResponse: undefined,

  isMediaStatusLoading: undefined,
  unIndexMediaSuccessResponse: undefined,
  unIndexMediaErrorResponse: undefined,
}

const reducer = createReducer<ProjectState>(
  projectInitialState,
  on(ProjectActions.resetProjectState, (state, { stateKey }) =>
    resetStateProperty<ProjectState>(state, projectInitialState, stateKey)
  ),
  on(ProjectActions.fetchProjectGroup, (state) => ({
    ...state,
    isProjectGroupLoading: true,
  })),
  on(
    ProjectActions.fetchProjectGroupSuccess,
    (state, { projectGroupSuccessResponse }) => ({
      ...state,
      projectGroupSuccessResponse,
      isProjectGroupLoading: false,
    })
  ),
  on(
    ProjectActions.fetchProjectGroupFailure,
    (state, { projectGroupErrorResponse }) => ({
      ...state,
      projectGroupErrorResponse,
      isProjectGroupLoading: false,
    })
  ),
  on(
    ProjectActions.fetchProjectFolderTreeWithCountSuccess,
    (state, { projectFolderTreeWithCountSuccessResponse }) => ({
      ...state,
      projectFolderTreeWithCountSuccessResponse,
    })
  ),
  on(
    ProjectActions.fetchProjectFolderTreeWithCountFailure,
    (state, { projectFolderTreeWithCountErrorResponse }) => ({
      ...state,
      projectFolderTreeWithCountErrorResponse,
    })
  ),
  on(
    ProjectActions.fetchSearchOptionsSuccess,
    (state, { searchOptionSuccessResponse }) => ({
      ...state,
      searchOptionSuccessResponse,
    })
  ),
  on(
    ProjectActions.fetchSearchOptionsFailure,
    (state, { searchOptionErrorResponse }) => ({
      ...state,
      searchOptionErrorResponse,
    })
  ),
  on(ProjectActions.fetchProjects, (state) => ({
    ...state,
    isProjectsLoading: true,
  })),
  on(
    ProjectActions.fetchProjectsSuccess,
    (state, { fetchProjectSuccessResponse }) => ({
      ...state,
      fetchProjectErrorResponse: undefined,
      fetchProjectSuccessResponse,
      isProjectsLoading: false,
    })
  ),
  on(
    ProjectActions.fetchProjectsFailure,
    (state, { fetchProjectErrorResponse }) => ({
      ...state,
      fetchProjectSuccessResponse: undefined,
      fetchProjectErrorResponse,
      isProjectsLoading: false,
    })
  ),
  on(
    ProjectActions.fetchProjectInfoSuccess,
    (state, { projectInfoSuccessResponse }) => ({
      ...state,
      projectInfoErrorResponse: undefined,
      projectInfoSuccessResponse,
    })
  ),
  on(
    ProjectActions.fetchProjectInfoFailure,
    (state, { projectInfoErrorResponse }) => ({
      ...state,
      projectInfoSuccessResponse: undefined,
      projectInfoErrorResponse,
    })
  ),
  on(ProjectActions.fetchCaseDetail, (state) => ({
    ...state,
    isCaseDetailLoading: true,
    // As soon as the case detail is requested, reset the selected case detail
    // to prevent showing the previous selected case detail and widget metrics
    selectedCaseDetail: undefined,
    caseDetailSuccessResponse: undefined,
  })),
  on(ProjectActions.fetchReviewSetSummaryDetail, (state) => ({
    ...state,
    isReviewSetSummaryDetailLoading: true,
  })),
  on(
    ProjectActions.fetchCaseDetailSuccess,
    (state, { caseDetailSuccessResponse }) => {
      return {
        ...state,
        caseDetailSuccessResponse,
        hasAnyFavoriteProject:
          caseDetailSuccessResponse?.caseDetailEntries.some(
            (item) => item.isFavoriteProject
          ),
        isCaseDetailLoading: false,
      }
    }
  ),
  on(
    ProjectActions.fetchReviewSetSummaryDetailSuccess,
    (state, { reviewSetSummaryDetailSuccess }) => ({
      ...state,
      reviewSetSummaryDetailSuccess,
      reviewSetSummaryDetailError: undefined,
      isReviewSetSummaryDetailLoading: false,
    })
  ),
  on(
    ProjectActions.fetchCaseDetailFailure,
    (state, { caseDetailErrorResponse }) => ({
      ...state,
      caseDetailErrorResponse,
      isCaseDetailLoading: false,
    })
  ),
  on(
    ProjectActions.fetchReviewSetSummaryDetailFailure,
    (state, { reviewSetSummaryDetailError }) => ({
      ...state,
      reviewSetSummaryDetailError,
      isReviewSetSummaryDetailLoading: false,
    })
  ),
  on(ProjectActions.updateCaseDetailRequestInfo, (state, action) => {
    // Remove null or undefined values from the request info object
    // to prevent overwriting the existing values with null or undefined
    const filteredRequestInfo = Object.fromEntries(
      Object.entries(action.caseDetailRequestInfo).filter(
        ([_, value]) => value !== null && value !== undefined
      )
    )

    return {
      ...state,
      caseDetailRequestInfo: {
        ...state.caseDetailRequestInfo,
        ...filteredRequestInfo,
        // Default sort order and filed is set to ProjectId and desc
        // if the sort order and field are not provided in the request info
        sortOrder: filteredRequestInfo.sortOrder || 'desc',
        sortField: filteredRequestInfo.sortOrder
          ? filteredRequestInfo.sortField
          : 'ProjectId',
      } as CaseDetailRequestInfo,
    }
  }),
  on(
    ProjectActions.updateReviewSetSummaryDetailRequestInfo,
    (state, action) => {
      // Remove null or undefined values from the request info object
      // to prevent overwriting the existing values with null or undefined
      const filteredRequestInfo = Object.fromEntries(
        Object.entries(action.reviewSetSummaryDetailRequestInfo).filter(
          ([_, value]) => value !== null && value !== undefined
        )
      )

      return {
        ...state,
        reviewSetSummaryDetailRequestInfo: {
          ...state.reviewSetSummaryDetailRequestInfo,
          ...filteredRequestInfo,
          // Default sort order and filed is set to ReviewSetName and desc
          // if the sort order and field are not provided in the request info
          sortOrder: filteredRequestInfo.sortOrder || 'desc',
          sortField: filteredRequestInfo.sortOrder
            ? filteredRequestInfo.sortField
            : 'CreatedOn',
        } as ReviewSetDetailRequestModel,
      }
    }
  ),
  on(ProjectActions.storeSelectedCaseDetail, (state, { selectedCaseIds }) => {
    // Pull out the selected case details from the caseDetailSuccessResponse
    const selectedCaseDetail =
      state.caseDetailSuccessResponse?.caseDetailEntries.filter((item) =>
        selectedCaseIds.includes(item.projectId)
      )
    return {
      ...state,
      selectedCaseDetail,
      hasAnyFavoriteProject:
        state?.caseDetailSuccessResponse?.caseDetailEntries.some(
          (item) => item.isFavoriteProject
        ),
    }
  }),
  on(
    ProjectActions.storeSelectedReviewSetSummaryDetail,
    (state, { selectedReviewSetSummaryDetail }) => {
      return {
        ...state,
        selectedReviewSetSummaryDetail,
      }
    }
  ),

  on(ProjectActions.toggleFavoriteProject, (state, { projectId }) => ({
    ...state,
    isFavoriteProjectToggleLoading: {
      ...state.isFavoriteProjectToggleLoading,
      [projectId]: true,
    },
  })),
  on(
    ProjectActions.toggleFavoriteProjectSuccess,
    (state, { projectId, isFavoriteProject }) => {
      // find the case in the case detail response and update the favorite status.
      const caseDetailEntries =
        state.caseDetailSuccessResponse?.caseDetailEntries.map((item) => {
          if (item.projectId === projectId) {
            return {
              ...item,
              isFavoriteProject,
            } as CaseDetailModel
          }
          return item
        })

      const caseDetailSuccessResponse = {
        ...state.caseDetailSuccessResponse,
        caseDetailEntries,
      }

      return {
        ...state,
        caseDetailSuccessResponse,
        hasAnyFavoriteProject:
          caseDetailSuccessResponse?.caseDetailEntries.some(
            (item) => item.isFavoriteProject
          ),
        isFavoriteProjectToggleLoading: {
          ...state.isFavoriteProjectToggleLoading,
          [projectId]: false,
        },
      }
    }
  ),
  on(ProjectActions.toggleFavoriteProjectFailure, (state, { projectId }) => ({
    ...state,
    isFavoriteProjectToggleLoading: {
      ...state.isFavoriteProjectToggleLoading,
      [projectId]: false,
    },
  })),
  on(ProjectActions.fetchProjectRights, (state) => ({
    ...state,
    isProjectRightLoading: true,
  })),
  on(ProjectActions.fetchProjectRightsSuccess, (state, { projectRights }) => {
    const projectIdsToRights: Record<number, UserRights[]> = {}
    // Process the incoming rights data
    Object.entries(projectRights).forEach(([right, projectIds]) => {
      const userRight = right as UserRights
      projectIds?.forEach((projectId) => {
        if (!projectIdsToRights[projectId]) {
          projectIdsToRights[projectId] = []
        }
        projectIdsToRights[projectId].push(userRight)
      })
    })
    return {
      ...state,
      rightsToProjectIds: {
        ...state.rightsToProjectIds,
        ...projectRights,
      },
      projectIdsToRights,
      projectRightErrorResponse: undefined,
      isProjectRightLoading: false,
    }
  }),
  on(
    ProjectActions.fetchProjectRightsFailure,
    (state, { projectRightErrorResponse }) => ({
      ...state,
      projectRightErrorResponse,
      projectRights: undefined,
      isProjectRightLoading: false,
    })
  ),
  on(ProjectActions.deleteReviewSet, (state, { projectId, reviewSetId }) => ({
    ...state,
    isReviewSetDeleteLoading: `${projectId}-${reviewSetId}`,
  })),
  on(
    ProjectActions.deleteReviewSetSuccess,
    (state, { reviewSetDeleteSuccess }) => ({
      ...state,
      reviewSetDeleteSuccess,
      reviewSetDeleteError: undefined,
      isReviewSetDeleteLoading: '',
    })
  ),
  on(
    ProjectActions.deleteReviewSetFailure,
    (state, { reviewSetDeleteError }) => ({
      ...state,
      reviewSetDeleteError,
      reviewSetDeleteSuccess: undefined,
      isReviewSetDeleteLoading: '',
    })
  ),
  on(ProjectActions.fetchTaggedDocumentTags, (state) => ({
    ...state,
    isTaggedDocumentTagsLoading: true,
  })),
  on(
    ProjectActions.fetchTaggedDocumentTagsSuccess,
    (state, { taggedDocumentTagsSuccess }) => ({
      ...state,
      taggedDocumentTagsSuccess,
      taggedDocumentTagsError: undefined,
      isTaggedDocumentTagsLoading: false,
    })
  ),
  on(
    ProjectActions.fetchTaggedDocumentTagsFailure,
    (state, { taggedDocumentTagsError }) => ({
      ...state,
      taggedDocumentTagsError,
      taggedDocumentTagsSuccess: undefined,
      isTaggedDocumentTagsLoading: false,
    })
  ),
  on(
    ProjectActions.updateReviewSetDocumentViewLoader,
    (state, { isReviewSetDocumentViewLoading }) => {
      return {
        ...state,
        isReviewSetDocumentViewLoading,
      }
    }
  ),
  on(ProjectActions.fetchReviewSetDocumentView, (state) => ({
    ...state,
    isReviewSetDocumentViewLoading: true,
  })),
  on(
    ProjectActions.fetchReviewSetDocumentViewSuccess,
    (state, { reviewSetDocumentViewSuccess }) => ({
      ...state,
      reviewSetDocumentViewSuccess,
      reviewSetDocumentViewError: undefined,
      isReviewSetDocumentViewLoading: false,
    })
  ),
  on(
    ProjectActions.fetchReviewSetDocumentViewFailure,
    (state, { reviewSetDocumentViewError }) => ({
      ...state,
      reviewSetDocumentViewError,
      reviewSetDocumentViewSuccess: undefined,
      isReviewSetDocumentViewLoading: false,
    })
  ),
  on(ProjectActions.fetchReviewSetBatch, (state) => ({
    ...state,
    isReviewSetBatchLoading: true,
  })),
  on(
    ProjectActions.fetchReviewSetBatchSuccess,
    (state, { reviewSetBatchSuccess }) => ({
      ...state,
      reviewSetBatchSuccess,
      reviewSetDeleteError: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(
    ProjectActions.fetchReviewSetBatchFailure,
    (state, { reviewSetBatchError }) => ({
      ...state,
      reviewSetBatchError,
      reviewSetBatchSuccess: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(ProjectActions.updateReviewSetBatchRequestInfo, (state, action) => {
    // Remove null or undefined values from the request info object
    const filteredRequestInfo = Object.fromEntries(
      Object.entries(action.reviewSetBatchRequestInfo).filter(
        ([key, value]) => value !== null && value !== undefined
      )
    )
    return {
      ...state,
      reviewSetBatchRequestInfo: {
        ...state.reviewSetBatchRequestInfo,
        ...filteredRequestInfo,
      } as ReviewSetBatchRequestModel,
    }
  }),
  on(
    ProjectActions.setSelectedBatchDetail,
    (state, { selectedBatchDetail }) => {
      return {
        ...state,
        selectedBatchDetail,
      }
    }
  ),
  on(ProjectActions.fetchReviewSetUserGroups, (state) => ({
    ...state,
    isReviewSetUserGroupLoading: true,
  })),
  on(
    ProjectActions.fetchReviewSetUserGroupsSuccess,
    (state, { reviewSetUserGroupSuccess }) => ({
      ...state,
      reviewSetUserGroupSuccess,
      reviewSetUserGroupError: undefined,
      isReviewSetUserGroupLoading: false,
    })
  ),
  on(
    ProjectActions.fetchReviewSetUserGroupsFailure,
    (state, { reviewSetUserGroupError }) => ({
      ...state,
      reviewSetUserGroupError,
      reviewSetUserGroupSuccess: undefined,
      isReviewSetUserGroupLoading: false,
    })
  ),
  on(ProjectActions.reassignReviewSetBatch, (state) => ({
    ...state,
    isReviewSetBatchLoading: true,
  })),
  on(
    ProjectActions.reassignReviewSetBatchSuccess,
    (state, { reviewSetReassignSuccess }) => ({
      ...state,
      reviewSetReassignSuccess,
      reviewSetReassignError: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(
    ProjectActions.reassignReviewSetBatchFailure,
    (state, { reviewSetReassignError }) => ({
      ...state,
      reviewSetReassignError,
      reviewSetReassignSuccess: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(ProjectActions.rebatchReviewSetBatch, (state) => ({
    ...state,
    isReviewSetBatchLoading: true,
  })),
  on(
    ProjectActions.rebatchReviewSetBatchSuccess,
    (state, { reviewSetRebatchSuccess }) => ({
      ...state,
      reviewSetRebatchSuccess,
      reviewSetReBatchError: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(
    ProjectActions.rebatchReviewSetBatchFailure,
    (state, { reviewSetReBatchError }) => ({
      ...state,
      reviewSetReBatchError,
      reviewSetRebatchSuccess: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(ProjectActions.deleteReviewSetBatch, (state) => ({
    ...state,
    isReviewSetBatchLoading: true,
  })),
  on(
    ProjectActions.deleteReviewSetBatchSuccess,
    (state, { reviewSetDeleteBatchSuccess }) => ({
      ...state,
      reviewSetDeleteBatchSuccess,
      reviewSetDeleteBatchError: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(
    ProjectActions.deleteReviewSetBatchFailure,
    (state, { reviewSetDeleteBatchError }) => ({
      ...state,
      reviewSetDeleteBatchError,
      reviewSetDeleteBatchSuccess: undefined,
      isReviewSetBatchLoading: false,
    })
  ),
  on(ProjectActions.checkUploadInvitationAccessSuccess, (state, action) => ({
    ...state,
    uploadInvitationAccessResponse: action.response,
  })),
  on(ProjectActions.fetchUnIndexMediaStatus, (state) => ({
    ...state,
    isMediaStatusLoading: true,
  })),
  on(
    ProjectActions.fetchUnIndexMediaStatusSuccess,
    (state, { unIndexMediaSuccessResponse }) => ({
      ...state,
      unIndexMediaSuccessResponse,
      isMediaStatusLoading: false,
      unIndexMediaErrorResponse: undefined,
    })
  ),
  on(
    ProjectActions.fetchUnIndexMediaStatusFailure,
    (state, { unIndexMediaErrorResponse }) => ({
      ...state,
      unIndexMediaErrorResponse,
      isMediaStatusLoading: false,
      unIndexMediaSuccessResponse: undefined,
    })
  )
)

export function projectReducer(
  state: ProjectState | undefined,
  action: Action
): ProjectState {
  return reducer(state, action)
}
