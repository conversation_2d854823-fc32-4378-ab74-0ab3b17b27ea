import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
  inject,
  signal,
} from '@angular/core'
import { ScrollingModule } from '@angular/cdk/scrolling'
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import {
  DocumentCodingFacade,
  ReviewPanelFacade,
} from '@venio/data-access/document-utility'
import {
  CompositeLayoutState,
  DocumentsFacade,
  ReviewPanelType,
} from '@venio/data-access/review'
import {
  DocumentCodingModel,
  DocumentCodingViewModel,
  CodingFieldPayloadModel,
  SelectedMultiCodingValueModel,
} from '@venio/shared/models/interfaces'
import { Remote, releaseProxy, wrap } from 'comlink'
import {
  Subject,
  filter,
  takeUntil,
  distinctUntilChanged,
  combineLatest,
  debounceTime,
} from 'rxjs'

import {
  DocumentCodingControlActionType,
  DocumentTagTreeListToggle,
  ShortcutKeyActions,
} from '@venio/shared/models/constants'
import { cloneDeep, isEqual } from 'lodash'
import { ActivatedRoute } from '@angular/router'
import { CodingFacade, DataAccessCommonModule } from '@venio/data-access/common'
import { CodingFieldComponent } from '../coding-field/coding-field.component'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { customDocumentCodingValidator } from '@venio/util/utilities'

declare type DocumentCodingExtractor = (
  fields: DocumentCodingModel[],
  filteredFields: DocumentCodingModel[]
) => DocumentCodingViewModel[]

@Component({
  selector: 'venio-document-coding',
  templateUrl: './document-coding.component.html',
  styleUrls: ['./document-coding.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    ScrollingModule,
    ReactiveFormsModule,
    IndicatorsModule,
    CodingFieldComponent,
    DataAccessCommonModule,
    InputsModule,
    DynamicHeightDirective,
    SvgLoaderDirective,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentCodingComponent implements OnInit, OnDestroy {
  public codingForm: FormGroup

  public documentCodingFields: DocumentCodingModel[]

  public codingFields: DocumentCodingViewModel[]

  public clonedCodingFields: DocumentCodingViewModel[]

  public filteredFields: DocumentCodingModel[]

  public currentField: DocumentCodingViewModel

  public selectedDocuments: number[]

  public currentDocument: number

  public isDocumentCodingLoading = false

  public isBulkDocument = false

  public isComponentReady = false

  private hideAllCodingFields = false

  public isCodingFieldsExists = signal(false)

  protected readonly codingActionEvent = DocumentCodingControlActionType

  public unsubscribed$: Subject<void> = new Subject<void>()

  @ViewChild('formElement') public formElement: ElementRef

  public isCodingExpanded = true

  public calculatedHeight: number

  public isHeightReady = false

  private layoutState: CompositeLayoutState = inject(CompositeLayoutState)

  // Define the icons for expand and collapse actions
  public svgIconForToggleControls = [
    {
      actionType: DocumentTagTreeListToggle.EXPAND_ALL,
      actionText: 'Expand All',
      iconPath: 'assets/svg/plus-circle-expand-icon-svg.svg',
      iconColor: '#9BD2A7',
      hoverColor: '#FFBB12',
    },
    {
      actionType: DocumentTagTreeListToggle.COLLAPSE_ALL,
      actionText: 'Collapse All',
      iconPath: 'assets/svg/minus-circle-collapse-svg.svg',
      iconColor: '#ED7425',
      hoverColor: '#FFBB12',
    },
  ]

  constructor(
    private documentsFacade: DocumentsFacade,
    private documentCodingFacade: DocumentCodingFacade,
    private codingFacade: CodingFacade,
    private reviewPanelFacade: ReviewPanelFacade,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef
  ) {}

  // Method to toggle expand/collapse
  public toggleExpandCollapse(actionType: DocumentTagTreeListToggle): void {
    if (actionType === DocumentTagTreeListToggle.EXPAND_ALL) {
      this.isCodingExpanded = true
    } else if (actionType === DocumentTagTreeListToggle.COLLAPSE_ALL) {
      this.isCodingExpanded = false
    }
  }

  public get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public get hasCodingFields() {
    return (): boolean => {
      return this.documentCodingFields && this.documentCodingFields.length > 0
    }
  }

  public getControl(fieldName: string, controlName: string): FormControl {
    return this.codingForm.get(`${fieldName}.${controlName}`) as FormControl
  }

  public codingFieldTrackByFn(
    _: number,
    field: DocumentCodingViewModel
  ): string {
    return `${field.customFieldInfoId}_${field.fieldName}`
  }

  public async extractDocumentCodingFields(
    fields: DocumentCodingModel[],
    filteredFields: DocumentCodingModel[]
  ): Promise<DocumentCodingViewModel[]> {
    const documentCodingExtractorHandler = new Worker(
      new URL(
        '../../../worker/document-coding-extractor.worker',
        import.meta.url
      ),
      { type: 'module' }
    )
    const documentCodingWorkerInterface: Remote<DocumentCodingExtractor> =
      wrap<DocumentCodingExtractor>(documentCodingExtractorHandler)
    const documentCodingViewModel = await documentCodingWorkerInterface(
      fields,
      filteredFields
    )
    //release the worker
    documentCodingWorkerInterface[releaseProxy]()
    return documentCodingViewModel
  }

  public ngOnInit(): void {
    this.fetchLoadingState()
    this.getCodingFields()
    this.selectCodingFields()
    this.#selectMultiValueCodingField()
    this.#searchDocumentCoding()
    this.#selectVisibleCodingFields()
    this.#selectCodingAddOrUpdateResponses()
    this.#selectCodingReOrderResponses()
    this.#selectIsDocumentCodingUpdated()
    this.#selectShortcutKeysAction()
  }

  private getCodingFields(): void {
    combineLatest([
      this.documentsFacade.getSelectedDocuments$,
      this.documentsFacade.isBulkDocument$,
    ])
      .pipe(
        filter(([selectedDocuments]) =>
          Boolean(
            selectedDocuments &&
              selectedDocuments.length > 0 &&
              !isEqual(selectedDocuments, this.selectedDocuments)
          )
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(([selectedDocuments, isBulkDocument]) => {
        this.selectedDocuments = selectedDocuments
        this.isBulkDocument = isBulkDocument
        this.currentDocument = this.selectedDocuments[0]
        this.fetchCodingFields()
      })
  }

  private fetchCodingFields(): void {
    this.storeIsDocumentCodingLoading(true)
    const codingFieldPayload: CodingFieldPayloadModel = {
      selectedDocuments: this.selectedDocuments,
      projectId: this.projectId,
      type: 'FIELD_CODING',
    }
    this.documentCodingFacade.getCodingFields(codingFieldPayload)
  }

  /**
   * Handles scenarios where the review panel is in a pop-out state and updates the
   * document tags in the pop-out review panel window when bulk tagging occurs.
   */

  #selectIsDocumentCodingUpdated(): void {
    this.documentCodingFacade.selectIsDocumentCodingUpdated$
      .pipe(
        filter((isDocumentTagUpdated) => Boolean(isDocumentTagUpdated)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.reset()
      })
  }

  #selectCodingAddOrUpdateResponses(): void {
    this.codingFacade.selectAddOrUpdateCodingFieldSuccessResponse$
      .pipe(
        filter((success) => !!success),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((success) => {
        if (!success) return
        this.reset()
      })
  }

  #selectCodingReOrderResponses(): void {
    this.codingFacade.selectCodingReorderSuccessResponse$
      .pipe(
        filter((success) => Boolean(success)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.reset()
      })
  }

  private selectCodingFields(): void {
    this.documentCodingFacade.selectDocumentCodingFields$
      .pipe(
        distinctUntilChanged(),
        filter((fields) => !!fields && fields.length > 0),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((fields) => {
        this.changeDetectorRef.markForCheck()
        this.documentCodingFields = fields
        this.isCodingFieldsExists.set(!this.hideAllCodingFields)
        if (this.hasCodingFields()) {
          this.storeIsDocumentCodingLoading(true)
          this.getCodingFieldDetails(
            this.documentCodingFields,
            this.filteredFields
          )
        }
      })
  }

  /**
   * This search code will be used only in bulk document processing.
   * It is not intended for use in individual document processing.
   *
   * @returns {void} This method does not return any value.
   */
  #searchDocumentCoding(): void {
    this.documentCodingFacade.selectSearchDocumentCoding$
      .pipe(
        filter(() =>
          Boolean(
            this.clonedCodingFields &&
              this.isComponentReady &&
              this.isBulkDocument
          )
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((search) => {
        this.#searchCodingNodes(search)
      })
  }

  #searchCodingNodes(filterTerm: string): void {
    const filterTermLowerCase = filterTerm.trim().toLowerCase()

    this.codingFields = filterTermLowerCase
      ? this.clonedCodingFields.filter((item) =>
          item.displayName.toLowerCase().includes(filterTermLowerCase)
        )
      : [...this.clonedCodingFields]
    this.changeDetectorRef.markForCheck()
  }

  #showHideCodingFields(filteredFields: DocumentCodingModel[]): void {
    if (this.clonedCodingFields) {
      this.codingFields = filteredFields?.[0]
        ? this.#filterCodingFields(filteredFields)
        : []
      this.changeDetectorRef.markForCheck()
    }

    const hasCodingFields = filteredFields?.[0] ? true : false
    this.isCodingFieldsExists.set(hasCodingFields)
  }

  #selectVisibleCodingFields(): void {
    this.documentCodingFacade.selectVisibleCodingFields$
      .pipe(
        distinctUntilChanged(),
        filter((filteredFields) => Boolean(filteredFields)),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((filteredFields) => {
        this.filteredFields = filteredFields
        this.hideAllCodingFields = !filteredFields?.[0]
        this.#showHideCodingFields(filteredFields)
      })
  }

  public createCodingForm(): void {
    const group = {}
    this.codingFields.forEach((field) => {
      const control = new FormControl(
        {
          value:
            field.updatedFieldValue ?? this.isBulkDocument
              ? ''
              : field.currentFieldValue,
          disabled: field.allowPredefinedCodingValuesOnly,
        },
        [customDocumentCodingValidator(field)]
      )

      const checkboxValue = this.isBulkDocument ? false : true
      const checkboxControl = new FormControl(checkboxValue)
      const fieldFormGroup = new FormGroup({
        fieldValue: control,
        isChecked: checkboxControl,
      })

      fieldFormGroup.valueChanges
        .pipe(debounceTime(200), takeUntil(this.unsubscribed$))
        .subscribe((value) => {
          this.updateFormValues(value, field)
        })
      group[field.fieldName] = fieldFormGroup
    })
    this.codingForm = new FormGroup(group)
    this.#checkCodingFormValid()
  }

  public updateFormValues(value, field): void {
    this.documentCodingFields = this.documentCodingFields.map((item) => ({
      ...item,
      updatedFieldValue:
        item.fieldName === field.fieldName && value.isChecked
          ? value.fieldValue
          : item.updatedFieldValue,
    }))
    const customFieldInfoId = this.documentCodingFields.find((item) => {
      return item.fieldName === field.fieldName
    })?.customFieldInfoId
    this.documentCodingFacade.updateCodingFields(
      this.documentCodingFields,
      customFieldInfoId,
      value.isChecked
    )
    this.#checkCodingFormValid()
  }

  #checkCodingFormValid(): void {
    this.codingForm.markAllAsTouched()
    this.codingForm.updateValueAndValidity()
    this.documentCodingFacade.setCodingDataValidStatus(this.codingForm.valid)
  }

  private fetchSelectedDocument(): void {
    this.documentsFacade.getSelectedDocuments$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((document) => {
        this.selectedDocuments = document
      })
  }

  /**
   * display tooltip for coding field
   */
  public codingInfoToggleTemplate(field: DocumentCodingViewModel): void {
    this.currentField = field
    this.changeDetectorRef.markForCheck()
  }

  public reset(): void {
    this.selectedDocuments = []
    this.#resetMultiCodingValue()
    this.getCodingFields()
  }

  public selectIsBulkDocument(): void {
    this.documentsFacade.isBulkDocument$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((isBulkDocument) => {
        this.isBulkDocument = isBulkDocument
      })
  }

  public fetchLoadingState(): void {
    this.documentCodingFacade.isDocumentCodeLoading$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((loading) => {
        this.isDocumentCodingLoading = loading
        this.changeDetectorRef.markForCheck()
      })
  }

  private storeIsDocumentCodingLoading(isDocumentCodeLoading: boolean): void {
    this.documentCodingFacade.setIsDocumentCodingLoading(isDocumentCodeLoading)
    this.changeDetectorRef.markForCheck()
  }

  private async getCodingFieldDetails(
    fields: DocumentCodingModel[],
    filteredFields: DocumentCodingModel[]
  ): Promise<void> {
    this.clonedCodingFields = await this.extractDocumentCodingFields(
      fields,
      filteredFields
    )
    this.#setCodingFields(filteredFields)
  }

  #setCodingFields(filteredFields: DocumentCodingModel[]): void {
    this.codingFields = this.#filterCodingFields(filteredFields)
    this.createCodingForm()
    this.storeIsDocumentCodingLoading(false)
    this.isComponentReady = true
    this.changeDetectorRef.markForCheck()
  }

  #filterCodingFields(
    filteredFields: DocumentCodingModel[]
  ): DocumentCodingViewModel[] {
    const clonedCodingFields = cloneDeep(this.clonedCodingFields)
    const orderedFields = this.layoutState.getFieldsSortedByLayoutOrder(
      clonedCodingFields,
      'displayName',
      ReviewPanelType.Coding
    )
    const filteredFieldIds = filteredFields?.map((x) => x.customFieldInfoId)
    return filteredFieldIds?.[0]
      ? orderedFields.filter(({ customFieldInfoId }) =>
          filteredFieldIds.includes(customFieldInfoId)
        )
      : orderedFields
  }

  #selectMultiValueCodingField(): void {
    this.documentCodingFacade.selectedMultiCodingValue$
      .pipe(
        distinctUntilChanged(),
        filter((selectedMulitCodingField) =>
          Boolean(selectedMulitCodingField?.selectedNewValues)
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((selectedMulitCodingField) => {
        this.#updateMultiCodingValue(selectedMulitCodingField)
        this.changeDetectorRef.markForCheck()
      })
  }

  #updateMultiCodingValue(
    selectedMulitCodingField: SelectedMultiCodingValueModel
  ): void {
    const selectedField = selectedMulitCodingField.selectedField
    const multiValuedCodingOptions =
      selectedMulitCodingField.selectedOperationType ?? 0
    const mappedCodingFields = this.codingFields.map((field) => ({
      ...field,
      updatedFieldValue:
        selectedField.fieldName === field.fieldName
          ? selectedMulitCodingField.selectedValues
          : field.updatedFieldValue,
      multiValuedCodingOptions:
        selectedField.fieldName === field.fieldName
          ? multiValuedCodingOptions
          : field.multiValuedCodingOptions,
    }))
    this.documentCodingFields = this.documentCodingFields.map((field) => ({
      ...field,
      updatedFieldValue:
        selectedField.fieldName === field.fieldName
          ? selectedMulitCodingField.selectedValues
          : field.updatedFieldValue,
      multiValuedCodingOptions:
        selectedField.fieldName === field.fieldName
          ? multiValuedCodingOptions
          : field.multiValuedCodingOptions,
    }))
    this.codingFields = mappedCodingFields
    const fieldFormGroup = this.codingForm.get(selectedField.fieldName)
    fieldFormGroup.patchValue({
      fieldValue: selectedMulitCodingField.selectedValues,
    })
  }

  #resetMultiCodingValue(): void {
    this.documentCodingFacade.resetDocumentCodingState([
      'selectedMultiCodingValue',
      'updatedCodingFieldInfoIds',
      'documentCodingFields',
      'isCodingDataModified',
      'isDocumentCodingUpdated',
    ])
  }

  #selectShortcutKeysAction(): void {
    this.reviewPanelFacade.getShortcutKeysAction
      .pipe(
        filter((action) => action === ShortcutKeyActions.UNDO_TAG_CODING),
        takeUntil(this.unsubscribed$)
      )
      .subscribe(() => {
        this.#setCodingFields(this.filteredFields)
      })
  }

  public onFormHeightSet(height: number): void {
    this.calculatedHeight = height
    this.isHeightReady = true
  }

  public ngOnDestroy(): void {
    this.#resetMultiCodingValue()
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }
}
