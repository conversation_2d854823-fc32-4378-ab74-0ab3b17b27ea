import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { ConverToImageFormService } from '../../services/convert-to-image-form.service'
import { FormGroup } from '@angular/forms'
import { Subject, debounceTime, takeUntil } from 'rxjs'
import { ConvertToImageFacade } from '@venio/data-access/review'

@Component({
  selector: 'venio-advanced-option',
  templateUrl: './advanced-option.component.html',
  styleUrl: './advanced-option.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdvancedOptionComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  private readonly toDestroy$ = new Subject<void>()

  public projectPageLimit: number

  public prifixDownloadList: string[] = ['TEXT', 'FIELD']

  public customFielddb = []

  public generateImageRadioEnabled = false

  public dpiOptions: number[] = [96, 200, 300, 600]

  public get isPrefixTypeText(): boolean {
    return (
      this.imageForm.get('systemBates.prefixType')?.value?.toString() === 'TEXT'
    )
  }

  public get imageForm(): FormGroup {
    return this.convertToImageFormService.imageForm
  }

  constructor(
    private readonly convertToImageFacade: ConvertToImageFacade,
    private readonly convertToImageFormService: ConverToImageFormService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  public ngOnInit(): void {
    this.getCustomFields()
    this.enableDisableGenerateBatesOptions()
    this.enableDisableCustomPageLimit()
    this.initProjectPageLimitAndStartingNumber()
    this.initCustomFieldsLoad()
    this.enableDisableCustomDimensions()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngAfterViewInit(): void {
    this.convertToImageFormService.resetForm()
    this.cdr.markForCheck()
  }

  public initProjectPageLimitAndStartingNumber(): void {
    this.convertToImageFacade.getProjectPageLimit$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((projectPageLimit: number) => {
        if (projectPageLimit !== null) {
          this.projectPageLimit = projectPageLimit
          this.cdr.markForCheck()
        }
      })

    this.convertToImageFacade.getBatesStartNumber$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((startNumber) => {
        if (startNumber !== null) {
          this.imageForm.get('systemBates.startingNum')?.setValue(startNumber)
          this.cdr.markForCheck()
        }
      })
  }

  public getCustomFields(): void {
    this.convertToImageFacade.fetchImageCustomFields()
  }

  public initCustomFieldsLoad(): void {
    this.convertToImageFacade.getCustomFields$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((customFields: string[]) => {
        if (customFields !== null) {
          this.customFielddb = customFields
        }
        this.cdr.markForCheck()
      })
  }

  public enableDisableCustomPageLimit(): void {
    const pageLimitControl = this.imageForm.get('pageLimitOption')

    if (pageLimitControl?.value) {
      pageLimitControl.valueChanges
        .pipe(debounceTime(100), takeUntil(this.toDestroy$))
        .subscribe({
          next: (selectedValue) => {
            if (selectedValue === 'CUSTOM_PAGE_LIMIT') {
              this.imageForm.get('customMaxpageLimit')?.enable()
            } else {
              this.imageForm.get('customMaxpageLimit')?.disable()
            }
          },
        })
    }
  }

  public enableDisableCustomDimensions(): void {
    const dimensionControl = this.imageForm.get(
      'imageDimensionSettings.imageDimension'
    )

    if (dimensionControl?.value) {
      dimensionControl.valueChanges
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((selectedValue) => {
          if (selectedValue === 'CUSTOM') {
            this.imageForm.get('imageDimensionSettings.customWidth')?.enable()
            this.imageForm.get('imageDimensionSettings.customHeight')?.enable()
            this.imageForm.get('imageDimensionSettings.dpi')?.enable()
          } else {
            this.imageForm.get('imageDimensionSettings.customWidth')?.disable()
            this.imageForm.get('imageDimensionSettings.customHeight')?.disable()
            this.imageForm.get('imageDimensionSettings.dpi')?.disable()
          }
          this.cdr.markForCheck()
        })
    }
  }

  public enableDisableGenerateBatesOptions(): void {
    const generateBatesControl = this.imageForm.get('systemBates')

    if (generateBatesControl?.value) {
      generateBatesControl
        .get('generateBates')
        .valueChanges.pipe(debounceTime(100), takeUntil(this.toDestroy$))
        .subscribe({
          next: (isChecked) => {
            if (isChecked) {
              this.imageForm.get('systemBates.prefixText')?.enable()
              this.imageForm.get('systemBates.prefixField')?.enable()
              this.imageForm.get('systemBates.padding')?.enable()
              this.imageForm.get('systemBates.startingNum')?.enable()
              this.imageForm.get('systemBates.brandingBates')?.enable()
              this.imageForm.get('systemBates.prefixType')?.enable()
              this.generateImageRadioEnabled = true
            } else {
              this.imageForm.get('systemBates.prefixText')?.disable()
              this.imageForm.get('systemBates.prefixField')?.disable()
              this.imageForm.get('systemBates.padding')?.disable()
              this.imageForm.get('systemBates.startingNum')?.disable()
              this.imageForm.get('systemBates.brandingBates')?.disable()
              this.imageForm.get('systemBates.prefixType')?.disable()
              this.generateImageRadioEnabled = false
            }
          },
        })
    }
  }
}
