/**
 * Interface for the 'ConvertToImage' data
 */
export interface ConvertToImageEntity {
  id: string | number // Primary ID
  name: string
}

export interface BulkImageRequestModel {
  userId?: number
  projectId: number
  selectedFileIds?: number[]
  searchTempTableName: string
  unSelectedFileIds?: number[]
  isBatchSelected: boolean
  launch: boolean
  relaunch: boolean
  selectedImageFileTypeList?: SelectedImageFileTypeDetailsRequestModel[]
  computeSummary: boolean
  systemBates: SystemBatesModel
  customMaxpageLimit: number
  outputImageDimension: ImageDimensionSettings
}

export interface SelectedImageFileTypeDetailsRequestModel {
  fileTypeGroupId: number
  myPageLimit: string
  cnt: number
  imagingEngine: string
  colorConversion: string
}

export interface BulkImageResponseModel {
  imageSummaryResponseModelList: ImageSummaryResponseModel[]
  imageFileTypeDetailsResponseModelList: ImageFileTypeDetailsResponseModel[]
  projectMaxPageLimit: number
  systemBates: SystemBatesModel
}

export interface ImageSummaryResponseModel {
  titleKey: string
  countValue: number
}

export interface ImageFileTypeDetailsResponseModel {
  fileTypeGroup: string
  fileTypeGroupId: number
  cNT: number
  myPageLimit: string
  imagingEngine: string
  colorConversion: string
  engineDb: any
  colorDb: any
  pageLimitdb: any
  selected: boolean
}

export interface SystemBatesModel {
  prefixText: string
  prefixField: string
  padding: number
  startingNum: number
  generateBates: boolean
  brandingBates: boolean
  autogenerateTiff: boolean
  ignoreAutoTiffJobsForMediaProcessingStatus: boolean
  prefixType: string
}

export interface BulkImageJobDetailResponseModel {
  JobId: string
  UserName: string
  DocumentCount: string
  StartTime: string
  EndTime: string
  TotalTime: string
  Status: string
}

export interface ImageDimensionSettings {
  originalImageDimension: boolean
  defaultDimension: boolean
  customDimension: boolean
  height: number | null
  width: number | null
  dpi: number | null
}
