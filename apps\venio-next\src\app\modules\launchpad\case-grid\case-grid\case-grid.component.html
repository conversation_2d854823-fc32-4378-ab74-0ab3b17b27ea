<div #containerElement class="t-flex-1 t-relative">
  <kendo-grid
    venioDynamicHeight
    [extraSpacing]="20"
    class="t-w-full t-min-h-[20rem] t-grid t-border-b-1 t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto v-case-launchpad-grid"
    [data]="gridView()"
    [loading]="isCaseDetailLoading()"
    [skip]="skip"
    [pageSize]="pageSize"
    [rowHeight]="34"
    [sortable]="true"
    [sort]="sort"
    [groupable]="false"
    [reorderable]="false"
    [resizable]="true"
    [trackBy]="caseTrackByFn"
    [selectable]="{ mode: 'multiple', cell: false, checkboxOnly: true }"
    [selectedKeys]="selectedProjectIds()"
    (pageChange)="handlePagingForVirtualScroll($event)"
    (filterChange)="caseFilterChange($event)"
    (selectedKeysChange)="selectCase($event)"
    (sortChange)="caseSortOrderChange($event)"
    scrollable="virtual"
    kendoGridSelectBy="projectId"
    filterable="menu">
    <ng-template kendoGridNoRecordsTemplate>
      <p *ngIf="!isCaseDetailLoading()">No records found</p>
    </ng-template>
    <kendo-grid-column
      field="sn"
      [width]="45"
      title="#"
      [sortable]="false"
      headerClass="t-text-primary"
      [filterable]="false" />
    <kendo-grid-checkbox-column [showSelectAll]="false" [width]="40" />
    <kendo-grid-column
      field="projectName"
      title="Case Name"
      [sortable]="true"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Case Name"
          >Case Name</span
        >
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="t-flex t-gap-2 t-items-center">
          @if(!isProjectDatabaseCompatible(dataItem.projectUpdatedVersion)) {
          <span
            venioSvgLoader
            kendoPopoverAnchor
            [popover]="projectCompatibility"
            showOn="hover"
            svgUrl="assets/svg/icon-warning-error-theme-triangle.svg"
            height="16px"
            width="16px">
            <kendo-loader size="small"></kendo-loader>
          </span>
          <kendo-popover #projectCompatibility [width]="250" position="right">
            <ng-template kendoPopoverBodyTemplate>
              <p class="t-text-sm t-text-error">
                Project database is not compatible with current version ({{
                  dataItem.projectUpdatedVersion
                }}). Please contact administrator.
              </p>
            </ng-template>
          </kendo-popover>
          }
          <span
            venioSvgLoader
            kendoTooltip
            *ngIf="getCaseTypeDetail(dataItem.caseType) as caseDetail"
            [svgUrl]="caseDetail.iconUrl"
            [title]="caseDetail.name"
            height="16px"
            width="16px">
          </span>
          {{ dataItem.projectName }}
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [sortable]="false"
      field="clientName"
      title="Client"
      headerClass="t-text-primary">
      <ng-template
        kendoGridFilterMenuTemplate
        let-column="column"
        let-filterService="filterService">
        <div class="t-flex t-w-52 t-text-[#1DBADC] t-font-semibold">
          Filter By Client
        </div>
        <div class="t-flex t-w-52 t-flex-col t-gap-3">
          <div>
            <kendo-textbox
              class="k-textbox"
              placeholder="Search Client"
              [(ngModel)]="clientNameSearchTerm" />
          </div>
          <div
            class="t-flex t-flex-col t-gap-2 t-overflow-y-hidden t-max-h-[175px] t-border-t-[2px] t-pt-3 t-mt-1 t-border-[#cccccc] t-border-dashed">
            <kendo-treelist
              [kendoTreeListFlatBinding]="filteredClientList()"
              class="t-overflow-y-auto"
              idField="clientId"
              [height]="175"
              kendoTreeListSelectable
              [selectable]="{ mode: 'row', multiple: true, drag: false }"
              [(selectedItems)]="selectedClients"
              (selectedItemsChange)="clientItemSelected($event, filterService)"
              [hideHeader]="true">
              <kendo-treelist-checkbox-column
                [width]="40"
                [class]="'!t-border-0'"
                [columnMenu]="false"
                [checkChildren]="true"
                [showSelectAll]="true" />
              <kendo-treelist-column
                [class]="'!t-border-0'"
                field="clientName" />
            </kendo-treelist>
          </div>
        </div>
      </ng-template>
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Client"
          >Client</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [sortable]="false"
      field="custodianCount"
      [width]="125"
      title="Term"
      [filterable]="false"
      headerClass="t-text-primary"
      class="!t-text-right !t-pr-[21px]">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Custodians"
          >Custodians</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [sortable]="false"
      field="documentCount"
      [width]="125"
      title="Documents"
      [filterable]="false"
      headerClass="t-text-primary"
      class="!t-text-right !t-pr-[21px]">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Documents"
          >Documents</span
        >
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      [sortable]="false"
      field="projectId"
      [width]="240"
      title="Created By & On"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridHeaderTemplate let-column>
        <span
          kendoTooltip
          class="t-text-ellipsis t-overflow-hidden"
          title="Created By & On"
          >Created By & On</span
        >
      </ng-template>

      <ng-template kendoGridCellTemplate let-dataItem>
        <p
          class="t-inline-block t-truncate t-overflow-hidden"
          kendoTooltip
          [title]="dataItem.projectCreator">
          {{ dataItem.projectCreator }}
        </p>
        <p class="t-text-xs t-text-[#999999]">
          {{ dataItem.caseCreatedDate }} {{ dataItem.caseCreatedTime }}
        </p>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column
      title="Actions"
      [width]="200"
      headerClass="t-text-primary"
      [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem>
        <ng-template #actionPlaceholder>
          <div class="t-flex t-flex-row t-gap-2">
            <kendo-skeleton
              *ngFor="let n of [1, 2, 3, 4]"
              height="25px"
              width="25px"
              shape="rectangle"
              class="t-rounded-md" />
          </div>
        </ng-template>
        @defer {
        <venio-case-grid-actions
          *ngIf="dataItem"
          [rowDataItem]="dataItem"
          (actionInvoked)="forwardActionControlClick($event, dataItem)" />
        } @placeholder {
        <ng-container *ngTemplateOutlet="actionPlaceholder" />
        }
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
