import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as ProjectActions from './project.actions'
import * as ProjectSelectors from './project.selectors'
import { ProjectState } from './project.reducer'
import {
  CaseDetailRequestInfo,
  ReviewSetBatchModel,
  ReviewSetBatchRequestModel,
  ReviewSetDetailRequestModel,
  ReviewSetEntry,
} from '@venio/shared/models/interfaces'

type ProjectStateKeys = keyof ProjectState | Array<keyof ProjectState>
@Injectable({ providedIn: 'root' })
export class ProjectFacade {
  public readonly selectIsProjectGroupLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('isProjectGroupLoading'))
  )

  public readonly selectProjectGroupSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('projectGroupSuccessResponse')
    )
  )

  public readonly selectProjectGroupErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('projectGroupErrorResponse')
    )
  )

  public readonly selectProject$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('fetchProjectSuccessResponse')
    )
  )

  public readonly selectProjectFolderTreeWithCountSuccessResponse$ =
    this.store.pipe(
      select(
        ProjectSelectors.getStateFromProjectStore(
          'projectFolderTreeWithCountSuccessResponse'
        )
      )
    )

  public readonly selectProjectFolderTreeWithCountErrorResponse$ =
    this.store.pipe(
      select(
        ProjectSelectors.getStateFromProjectStore(
          'projectFolderTreeWithCountErrorResponse'
        )
      )
    )

  public readonly selectSearchOptionSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('searchOptionSuccessResponse')
    )
  )

  public readonly selectSearchOptionErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('searchOptionErrorResponse')
    )
  )

  public readonly selectProjectInfoSuccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('projectInfoSuccessResponse')
    )
  )

  public readonly selectProjectInfoErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('projectInfoErrorResponse')
    )
  )

  public readonly selectIsCaseDetailLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('isCaseDetailLoading'))
  )

  public readonly selectIsReviewSetSummaryDetailLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore(
        'isReviewSetSummaryDetailLoading'
      )
    )
  )

  public readonly selectCaseDetail$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('caseDetailSuccessResponse')
    )
  )

  public readonly selectReviewSetSummaryDetail$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetSummaryDetailSuccess')
    )
  )

  public readonly selectCaseDetailErrorResponse$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('caseDetailErrorResponse'))
  )

  public readonly selectReviewSetSummaryDetailError$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetSummaryDetailError')
    )
  )

  public readonly selectCaseDetailPagingInfo$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('caseDetailRequestInfo'))
  )

  public readonly selectReviewSetSummaryDetailPagingInfo$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore(
        'reviewSetSummaryDetailRequestInfo'
      )
    )
  )

  public readonly selectSelectedCaseDetail$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('selectedCaseDetail'))
  )

  public readonly selectSelectedReviewSetSummaryDetail$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore(
        'selectedReviewSetSummaryDetail'
      )
    )
  )

  public readonly isFavoriteProjectToggleLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore(
        'isFavoriteProjectToggleLoading'
      )
    )
  )

  public readonly hasAnyFavoriteProject$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('hasAnyFavoriteProject'))
  )

  public readonly isProjectRightLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('isProjectRightLoading'))
  )

  public readonly selectUploadInvitationAccessResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore(
        'uploadInvitationAccessResponse'
      )
    )
  )

  /**
   * Returns a dictionary of projectIds to rights.
   * The key is a right name, and the value is an array of projectIds which are allowed this right.
   * @see ProjectState
   *
   * @example
   * {
   *  ALLOW_TO_VIEW: [1, 2, 3],
   * }
   *
   * @returns {Observable<Record<number, ProjectRights>}
   */
  public readonly selectRightsToProjectIds$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('rightsToProjectIds'))
  )

  /**
   * Returns a dictionary of projectIds to rights.
   * The key is a project id, and the value is an array of rights which are allowed for this project.
   * @see ProjectState
   *
   * @example
   * {
   * 1: ['ALLOW_TO_VIEW', 'ALLOW_TO_EDIT'],
   * }
   * @returns {Observable<Record<ProjectRights, number[]>}
   */
  public readonly selectProjectIdsToRights$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('projectIdsToRights'))
  )

  public readonly selectProjectRightErrorResponse$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('projectRightErrorResponse')
    )
  )

  public readonly selectIsReviewSetDeleteLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('isReviewSetDeleteLoading')
    )
  )

  public readonly selectReviewSetDeleteSuccess$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetDeleteSuccess'))
  )

  public readonly selectReviewSetDeleteError$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetDeleteError'))
  )

  public readonly selectIsTaggedDocumentTagsLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('isTaggedDocumentTagsLoading')
    )
  )

  public readonly selectTaggedDocumentTagsSuccess$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('taggedDocumentTagsSuccess')
    )
  )

  public readonly selectTaggedDocumentTagsError$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('taggedDocumentTagsError'))
  )

  public readonly selectIsReviewSetDocumentViewLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore(
        'isReviewSetDocumentViewLoading'
      )
    )
  )

  public readonly selectReviewSetDocumentViewSuccess$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetDocumentViewSuccess')
    )
  )

  public readonly selectReviewSetDocumentViewError$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetDocumentViewError')
    )
  )

  public readonly selectIsReviewSetBatchLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('isReviewSetBatchLoading'))
  )

  public readonly selectReviewSetBatchDetail$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetBatchSuccess'))
  )

  public readonly selectReviewSetBatchErrorResponse$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetBatchError'))
  )

  public readonly selectReviewSetBatchRequestInfo$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetBatchRequestInfo')
    )
  )

  public readonly selecteSelectedBatchDetail$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('selectedBatchDetail'))
  )

  public readonly selectIsReviewSetUserGroupLoading$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('isReviewSetUserGroupLoading')
    )
  )

  public readonly selectReviewSetUserGroupDetail$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetUserGroupSuccess')
    )
  )

  public readonly selectReviewSetUserGroupErrorResponse$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetUserGroupError'))
  )

  public readonly selectReviewSetRebatchSuccess$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetRebatchSuccess'))
  )

  public readonly selectReviewSetReBatchError$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetReBatchError'))
  )

  public readonly selectReviewSetDeleteBatchSuccess$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetDeleteBatchSuccess')
    )
  )

  public readonly selectReviewSetDeleteBatchError$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetDeleteBatchError')
    )
  )

  public readonly selectReviewSetReassignSuccess$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('reviewSetReassignSuccess')
    )
  )

  public readonly selectReviewSetReassignError$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('reviewSetReassignError'))
  )

  constructor(private readonly store: Store) {}

  public fetchProjectGroup(projectId: number): void {
    this.store.dispatch(ProjectActions.fetchProjectGroup({ projectId }))
  }

  public fetchProjects(): void {
    this.store.dispatch(ProjectActions.fetchProjects())
  }

  public fetchProjectInfo(projectId: number): void {
    this.store.dispatch(ProjectActions.fetchProjectInfo({ projectId }))
  }

  public fetchSearchOptions(projectId: number): void {
    this.store.dispatch(ProjectActions.fetchSearchOptions({ projectId }))
  }

  public deleteReviewSet(projectId: number, reviewSetId: number): void {
    this.store.dispatch(
      ProjectActions.deleteReviewSet({ projectId, reviewSetId })
    )
  }

  public readonly selectIsMediaStatusLoading$ = this.store.pipe(
    select(ProjectSelectors.getStateFromProjectStore('isMediaStatusLoading'))
  )

  public readonly selectunIndexMediaSuccess$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('unIndexMediaSuccessResponse')
    )
  )

  public readonly selectunIndexMediaError$ = this.store.pipe(
    select(
      ProjectSelectors.getStateFromProjectStore('unIndexMediaErrorResponse')
    )
  )

  /**
   * Resetting the Project Group State
   * @returns {void}
   */
  public resetProjectGroupState(): void {
    this.store.dispatch(
      ProjectActions.resetProjectState({
        stateKey: [
          'isProjectGroupLoading',
          'projectGroupErrorResponse',
          'projectGroupSuccessResponse',
        ],
      })
    )
  }

  public fetchProjectFolderTreeWithCount(projectId: number): void {
    this.store.dispatch(
      ProjectActions.fetchProjectFolderTreeWithCount({ projectId })
    )
  }

  /**
   * Resetting a specific property of the Project State
   * @param {ProjectStateKeys}stateKey - state keys
   * @returns {void}
   */
  public resetProjectState(stateKey: ProjectStateKeys): void {
    this.store.dispatch(ProjectActions.resetProjectState({ stateKey }))
  }

  public fetchCaseDetail(): void {
    this.store.dispatch(ProjectActions.fetchCaseDetail())
  }

  public fetchReviewSetSummaryDetail(): void {
    this.store.dispatch(ProjectActions.fetchReviewSetSummaryDetail())
  }

  public updateCaseDetailRequestInfo(
    caseDetailRequestInfo: Partial<CaseDetailRequestInfo>
  ): void {
    this.store.dispatch(
      ProjectActions.updateCaseDetailRequestInfo({ caseDetailRequestInfo })
    )
  }

  public updateReviewSetDetailRequestInfo(
    reviewSetSummaryDetailRequestInfo: Partial<ReviewSetDetailRequestModel>
  ): void {
    this.store.dispatch(
      ProjectActions.updateReviewSetSummaryDetailRequestInfo({
        reviewSetSummaryDetailRequestInfo,
      })
    )
  }

  public storeSelectedCaseDetail(selectedCaseIds: number[]): void {
    this.store.dispatch(
      ProjectActions.storeSelectedCaseDetail({ selectedCaseIds })
    )
  }

  public storeSelectedReviewSet(
    selectedReviewSetSummaryDetail: ReviewSetEntry[]
  ): void {
    this.store.dispatch(
      ProjectActions.storeSelectedReviewSetSummaryDetail({
        selectedReviewSetSummaryDetail,
      })
    )
  }

  public toggleFavouriteProject(
    projectId: number,
    isFavoriteProject: boolean
  ): void {
    this.store.dispatch(
      ProjectActions.toggleFavoriteProject({ projectId, isFavoriteProject })
    )
  }

  public fetchProjectRights(projectIds: number[]): void {
    this.store.dispatch(ProjectActions.fetchProjectRights({ projectIds }))
  }

  public fetchTaggedDocumentTags(projectId: number, reviewSetId: number): void {
    this.store.dispatch(
      ProjectActions.fetchTaggedDocumentTags({ projectId, reviewSetId })
    )
  }

  public updateReviewSetDocumentViewLoader(
    isReviewSetDocumentViewLoading: boolean | undefined
  ): void {
    this.store.dispatch(
      ProjectActions.updateReviewSetDocumentViewLoader({
        isReviewSetDocumentViewLoading,
      })
    )
  }

  public fetchReviewSetDocumentView(
    projectId: number,
    reviewSetId: number,
    payload: {
      searchResultTempTable: string
    }
  ): void {
    this.store.dispatch(
      ProjectActions.fetchReviewSetDocumentView({
        projectId,
        reviewSetId,
        payload,
      })
    )
  }

  public fetchReviewSetBatch(projectId: number, reviewSetId: number): void {
    this.store.dispatch(
      ProjectActions.fetchReviewSetBatch({ projectId, reviewSetId })
    )
  }

  public setSelectedBatchDetail(
    selectedBatchDetail: ReviewSetBatchModel[]
  ): void {
    this.store.dispatch(
      ProjectActions.setSelectedBatchDetail({ selectedBatchDetail })
    )
  }

  public updateReviewSetBatchRequestInfo(
    reviewSetBatchRequestInfo: Partial<ReviewSetBatchRequestModel>
  ): void {
    this.store.dispatch(
      ProjectActions.updateReviewSetBatchRequestInfo({
        reviewSetBatchRequestInfo,
      })
    )
  }

  public fetchReviewSetUserGroups(
    projectId: number,
    reviewSetId: number
  ): void {
    this.store.dispatch(
      ProjectActions.fetchReviewSetUserGroups({ projectId, reviewSetId })
    )
  }

  public rebatchReviewSetBatch(
    projectId: number,
    reviewSetId: number,
    batchIds: number[]
  ): void {
    this.store.dispatch(
      ProjectActions.rebatchReviewSetBatch({ projectId, reviewSetId, batchIds })
    )
  }

  public deleteReviewSetBatch(
    projectId: number,
    reviewSetId: number,
    batchIds: number[]
  ): void {
    this.store.dispatch(
      ProjectActions.deleteReviewSetBatch({
        projectId,
        reviewSetId,
        batchIds,
      })
    )
  }

  public reassignReviewSetBatch(
    projectId: number,
    reviewSetId: number,
    batchId: number,
    reviewerId: number
  ): void {
    this.store.dispatch(
      ProjectActions.reassignReviewSetBatch({
        projectId,
        reviewSetId,
        batchId,
        reviewerId,
      })
    )
  }

  public checkUploadInvitationAccess(
    projectId: number,
    userWiseToken: string
  ): void {
    this.store.dispatch(
      ProjectActions.checkUploadInvitationAccess({ projectId, userWiseToken })
    )
  }

  public fetchUnIndexMediaStatus(projectId: number): void {
    this.store.dispatch(ProjectActions.fetchUnIndexMediaStatus({ projectId }))
  }
}
