<kendo-treelist
  [hideHeader]="true"
  [initiallyExpanded]="false"
  [kendoTreeListFlatBinding]="treeData"
  idField="folderId"
  parentIdField="parentFolderId"
  class="!t-border-0 t-w-[calc(100%_-_1px)] t-h-full t-bg-[#FAFAFA]"
  kendoTreeListExpandable
  expandBy="folderId"
  [(expandedKeys)]="defaultExpandedFolderIds"
  [rowHeight]="36"
  [pageSize]="50"
  scrollable="virtual"
  [autoSize]="true"
  [columnMenu]="false"
  kendoTreeListSelectable
  (selectionChange)="onSelectionChange($event)"
  (cellClick)="onCellClick($event)"
  [(selectedItems)]="selected"
  itemKey="folderId">
  <ng-template kendoTreeListToolbarTemplate>
    <kendo-textbox
      class="!t-border-[#ccc] !t-w-full !t-flex"
      placeholder="Filter"
      (valueChange)="onFilter($event)">
    </kendo-textbox>
  </ng-template>
  <kendo-treelist-column
    [expandable]="true"
    [filterable]="true"
    field="folderName"
    title="folderName"
    [resizable]="true"
    class="!t-border-b-0 !t-py-[0.65rem] !t-flex t-cursor-pointer">
    <ng-template
      kendoTreeListCellTemplate
      let-dataItem
      let-isExpanded="isExpanded">
      <div class="t-flex t-gap-0.5 t-items-center">
        <span
          venioSvgLoader
          height="1rem"
          width="1rem"
          [svgUrl]="
            isExpanded
              ? 'assets/svg/icon-folder-fclv-open.svg'
              : 'assets/svg/icon-folder-fclv.svg'
          ">
          <kendo-loader size="small"></kendo-loader>
        </span>
        {{ dataItem.folderName }}
        <ng-container *ngIf="dataItem.fileCount > 0">
          &nbsp;({{ dataItem.fileCount }})
        </ng-container>
        &nbsp;&nbsp;
        <div
          *ngIf="dataItem.parentFolderId === null"
          class="t-flex t-gap-[0.5rem] t-absolute t-right-2"
          kendoTooltip>
          <button
            #expandBtn
            kendoButton
            class="t-p-0 t-cursor-pointer"
            fillMode="clear"
            title="Expand All"
            (click)="expandAllChildNodes(dataItem)">
            <span
              venioSvgLoader
              color="#9BD2A7"
              hoverColor="#FFBB12"
              applyEffectsTo="fill"
              [parentElement]="expandBtn.element"
              svgUrl="assets/svg/plus-circle-expand-icon-svg.svg"
              height="1rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
          <button
            kendoButton
            #collapseBtn
            class="t-p-0 t-cursor-pointer"
            fillMode="clear"
            title="Collapse All"
            (click)="collapseAllChildNodes(dataItem)">
            <span
              venioSvgLoader
              color="#ED7425"
              hoverColor="#FFBB12"
              applyEffectsTo="fill"
              [parentElement]="collapseBtn.element"
              svgUrl="assets/svg/minus-circle-collapse-svg.svg"
              height="1rem"
              width="1rem">
              <kendo-loader size="small"></kendo-loader>
            </span>
          </button>
        </div>
      </div>
    </ng-template>
  </kendo-treelist-column>
</kendo-treelist>
